"""
" service name
"""
SPORT_SERVICE_NAME = "sport"


"""
" service api version
"""
SPORT_API_VERSION = "1.0.0.1"


"""
" api id
"""
ROBOT_SPORT_API_ID_DAMP              = 1001
ROBOT_SPORT_API_ID_BALANCESTAND      = 1002
ROBOT_SPORT_API_ID_STOPMOVE          = 1003
ROBOT_SPORT_API_ID_STANDUP           = 1004
ROBOT_SPORT_API_ID_STANDDOWN         = 1005
ROBOT_SPORT_API_ID_RECOVERYSTAND     = 1006
ROBOT_SPORT_API_ID_MOVE              = 1008
ROBOT_SPORT_API_ID_SWITCHGAIT        = 1011
ROBOT_SPORT_API_ID_BODYHEIGHT        = 1013
ROBOT_SPORT_API_ID_SPEEDLEVEL        = 1015
ROBOT_SPORT_API_ID_TRAJECTORYFOLLOW  = 1018
ROBOT_SPORT_API_ID_CONTINUOUSGAIT    = 1019
ROBOT_SPORT_API_ID_MOVETOPOS         = 1036
ROBOT_SPORT_API_ID_SWITCHMOVEMODE    = 1038
ROBOT_SPORT_API_ID_VISIONWALK        = 1101
ROBOT_SPORT_API_ID_HANDSTAND         = 1039
ROBOT_SPORT_API_ID_AUTORECOVERY_SET  = 1040
ROBOT_SPORT_API_ID_FREEWALK          = 1045
ROBOT_SPORT_API_ID_CLASSICWALK       = 1049
ROBOT_SPORT_API_ID_FASTWALK          = 1050
ROBOT_SPORT_API_ID_FREEEULER         = 1051

"""
" error code
"""
# client side
SPORT_ERR_CLIENT_POINT_PATH = 4101
# server side
SPORT_ERR_SERVER_OVERTIME = 4201
SPORT_ERR_SERVER_NOT_INIT = 4202
