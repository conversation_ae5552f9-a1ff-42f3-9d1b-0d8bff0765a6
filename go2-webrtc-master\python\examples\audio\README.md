# Go2 机器狗音频接收演示

本示例展示了如何从Unitree Go2机器狗接收音频流并在本地播放。

## 功能特点

- 建立与Go2机器狗的WebRTC连接
- 接收Go2机器狗的音频数据流
- 使用PyAudio在本地播放接收到的音频

## 前提条件

- Unitree Go2机器狗（支持Air、Pro和Edu型号）
- 与机器狗处于同一网络的计算机
- Python 3.6+
- 以下Python包：
  - aiortc
  - PyAudio
  - numpy
  - python-dotenv
  - av (PyAV)

## 安装依赖

在项目根目录下运行以下命令安装所需依赖：

```bash
pip install -r python/requirements.txt
```

## 使用方法

1. 确保Go2机器狗已开机并连接到与计算机相同的网络
2. 创建一个`.env`文件并设置以下变量（或直接在命令行中指定）：
   ```
   GO2_IP=<机器狗的IP地址>
   GO2_TOKEN=<访问令牌>
   ```
3. 运行演示程序：
   ```bash
   python go2_audio_demo.py
   ```

   或者使用命令行参数：
   ```bash
   python go2_audio_demo.py --ip <机器狗IP地址> --token <访问令牌>
   ```
   
4. 程序会连接到机器狗并开始播放接收到的音频。按Ctrl+C可以停止程序。

## 问题排查

1. **无法连接到机器狗**：
   - 确认IP地址是否正确
   - 确认机器狗和计算机是否在同一网络
   - 确认防火墙没有阻止连接

2. **连接成功但听不到声音**：
   - 确认本地计算机的音频设备是否正常工作
   - 确认机器狗的麦克风是否开启和工作正常
   - 检查日志看是否有接收到音频帧的信息

3. **音频播放异常或卡顿**：
   - 可能是网络问题，尝试靠近路由器或使用有线网络
   - 检查CPU使用情况，确保计算机有足够资源处理音频 