#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Go2 WebRTC 音频演示程序
此程序演示如何连接Go2机器狗并接收其音频流
"""

import asyncio
import logging
import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径，以便可以导入go2_webrtc包
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from go2_webrtc.go2_connection import Go2Connection
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("go2_audio_demo")


def signal_handler(sig, frame):
    """处理Ctrl+C退出信号"""
    print("接收到退出信号，正在关闭...")
    asyncio.get_event_loop().stop()


async def on_validated():
    """验证成功时的回调函数"""
    logger.info("已成功验证与Go2的连接")


async def on_open():
    """数据通道打开时的回调函数"""
    logger.info("数据通道已打开，可以开始发送命令")


def on_message(message, parsed_message=None):
    """处理接收到的消息"""
    # 只记录文本消息
    if isinstance(message, str):
        try:
            logger.info(f"收到消息: {message[:100]}")
        except Exception:
            pass


async def main(args):
    """主函数"""
    # 加载环境变量
    load_dotenv()
    
    # 获取机器狗IP和TOKEN
    ip = args.ip or os.getenv("GO2_IP")
    token = args.token or os.getenv("GO2_TOKEN")
    
    if not ip:
        logger.error("请提供Go2的IP地址，可通过命令行参数--ip或环境变量GO2_IP提供")
        return
    
    if not token:
        logger.warning("未提供访问令牌，如果机器狗需要身份验证，连接可能会失败")
    
    logger.info(f"正在连接Go2 机器狗 (IP: {ip})")
    
    # 创建Go2Connection实例，启用音频
    connection = Go2Connection(
        ip=ip,
        token=token,
        on_validated=on_validated,
        on_message=on_message,
        on_open=on_open,
        enable_audio=True
    )
    
    try:
        # 连接到机器狗
        await connection.connect_robot()
        logger.info("成功连接到Go2，开始接收音频")
        
        # 保持程序运行直到手动中断
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"发生错误: {e}")
    finally:
        # 关闭连接并释放资源
        await connection.close()
        logger.info("已关闭连接")


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Go2 WebRTC 音频演示程序")
    parser.add_argument("--ip", help="Go2机器狗的IP地址")
    parser.add_argument("--token", help="Go2机器狗的访问令牌")
    args = parser.parse_args()
    
    # 运行主程序
    try:
        asyncio.run(main(args))
    except KeyboardInterrupt:
        print("程序已退出") 