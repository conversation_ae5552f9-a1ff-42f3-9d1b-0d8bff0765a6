import { Go2WebRTC } from "./go2webrtc.js";

// 全局速度倍数变量
globalThis.speedMultiplier = 0.5; // 默认50%速度

// 控制命令防抖变量
let lastControlCommand = { x: 0, y: 0, z: 0 };
let lastCommandTime = 0;
let lastMobileControlCommand = { x: 0, y: 0, z: 0 };
let lastMobileCommandTime = 0;
const COMMAND_THROTTLE_MS = 30; // 30ms防抖间隔

// Function to log messages to the console and the log window
function logMessage(text) {
  var log = document.querySelector("#log");
  var msg = document.getElementById("log-code");
  const timestamp = new Date().toLocaleTimeString();
  msg.textContent += `[${timestamp}] ${truncateString(text, 300)}\n`;
  log.scrollTop = log.scrollHeight;
}
globalThis.logMessage = logMessage;

// Function to update connection status
function updateConnectionStatus(connected, message = '') {
  const statusDot = document.getElementById('status-dot');
  const statusText = document.getElementById('status-text');
  const connectBtn = document.getElementById('connect-btn');
  const executeBtn = document.getElementById('execute-btn');
  
  if (connected) {
     statusDot.classList.add('connected');
     statusText.textContent = '已连接';
     connectBtn.innerHTML = '<i class="fas fa-unlink"></i> 断开连接';
     executeBtn.disabled = false;
   } else {
     statusDot.classList.remove('connected');
     statusText.textContent = message || '未连接';
     connectBtn.innerHTML = '<i class="fas fa-plug"></i> 连接';
     executeBtn.disabled = true;
   }
}

// 移动端触摸事件优化
function addMobileTouchSupport() {
  const buttons = document.querySelectorAll('.btn');
  buttons.forEach(button => {
    // 添加触摸开始事件
    button.addEventListener('touchstart', function(e) {
      if (!this.disabled) {
        this.style.transform = 'scale(0.95)';
      }
    }, { passive: true });
    
    // 添加触摸结束事件
    button.addEventListener('touchend', function(e) {
      if (!this.disabled) {
        this.style.transform = '';
      }
    }, { passive: true });
  });
  
  // 优化虚拟摇杆的触摸事件
  const joystickContainers = document.querySelectorAll('.corner-div');
  joystickContainers.forEach(container => {
    container.addEventListener('touchstart', function(e) {
      // 只阻止摇杆区域的默认行为
      e.stopPropagation();
    }, { passive: false });
    
    container.addEventListener('touchmove', function(e) {
      // 只阻止摇杆区域的滚动
      e.stopPropagation();
    }, { passive: false });
  });
}

// Function to show/hide video placeholder
function updateVideoDisplay(hasVideo) {
  // 主界面视频元素
  const videoFrame = document.getElementById('video-frame');
  const videoPlaceholder = document.getElementById('video-placeholder');
  
  // 手机控制界面视频元素
  const mobileVideoFrame = document.getElementById('mobile-video-frame');
  const mobileVideoPlaceholder = document.querySelector('.mobile-video-placeholder');
  
  if (hasVideo) {
    // 显示主界面视频
    if (videoFrame) {
      videoFrame.style.display = 'block';
    }
    if (videoPlaceholder) {
      videoPlaceholder.style.display = 'none';
    }
    
    // 显示手机界面视频
    if (mobileVideoFrame) {
      mobileVideoFrame.style.display = 'block';
    }
    if (mobileVideoPlaceholder) {
      mobileVideoPlaceholder.style.display = 'none';
    }
    
    console.log('视频显示已启用（主界面和手机界面）');
  } else {
    // 隐藏主界面视频，显示占位符
    if (videoFrame) {
      videoFrame.style.display = 'none';
    }
    if (videoPlaceholder) {
      videoPlaceholder.style.display = 'flex';
    }
    
    // 隐藏手机界面视频，显示占位符
    if (mobileVideoFrame) {
      mobileVideoFrame.style.display = 'none';
    }
    if (mobileVideoPlaceholder) {
      mobileVideoPlaceholder.style.display = 'flex';
    }
    
    console.log('视频显示已禁用（主界面和手机界面）');
  }
}

// Initialize video display
updateVideoDisplay(false);
globalThis.updateConnectionStatus = updateConnectionStatus;
globalThis.updateVideoDisplay = updateVideoDisplay;

// Function to load saved values from localStorage
function loadSavedValues() {
  const savedRobotIP = localStorage.getItem("robotIP");
  const savedRobotToken = localStorage.getItem("robotToken");

  if (savedRobotIP) {
    document.getElementById("robot-ip").value = savedRobotIP;
  }
  
  if (savedRobotToken) {
    document.getElementById("robot-token").value = savedRobotToken;
  }

  const commandSelect = document.getElementById("command");
  Object.entries(SPORT_CMD).forEach(([value, text]) => {
    const option = document.createElement("option");
    option.value = value;
    option.textContent = text;
    commandSelect.appendChild(option);
  });
}

// Function to save values to localStorage
function saveValuesToLocalStorage() {
  const robotIP = document.getElementById("robot-ip").value;
  const robotToken = document.getElementById("robot-token").value;

  localStorage.setItem("robotIP", robotIP);
  localStorage.setItem("robotToken", robotToken);
}

// Handle connect button click
function handleConnectClick() {
  const robotIP = document.getElementById("robot-ip").value;
  const robotToken = document.getElementById("robot-token").value;

  // Check if already connected
  if (globalThis.rtc && globalThis.rtc.connected) {
    // Disconnect
    globalThis.rtc.disconnect();
    globalThis.rtc = null;
    updateConnectionStatus(false);
    updateVideoDisplay(false);
    logMessage('已断开与机器人的连接');
    return;
  }

  // Validate inputs
   if (!robotIP.trim()) {
     logMessage('错误：请输入机器人IP地址');
     return;
   }

  console.log("Robot IP:", robotIP);
  console.log("Robot Token:", robotToken || "(空)");
  logMessage(`正在连接机器人 ${robotIP}...`);
  updateConnectionStatus(false, '连接中...');

  // Save the values to localStorage
  saveValuesToLocalStorage();

  try {
    // Initialize RTC
    globalThis.rtc = new Go2WebRTC(robotToken, robotIP);
    
    // Add connection event handlers
    globalThis.rtc.onConnectionStateChange = (state) => {
       logMessage(`连接状态: ${state}`);
       if (state === 'connected') {
         updateConnectionStatus(true);
         logMessage('成功连接到机器人！');
       } else if (state === 'disconnected' || state === 'failed') {
         updateConnectionStatus(false);
         updateVideoDisplay(false);
       }
     };
    
    globalThis.rtc.onVideoTrack = (stream) => {
      const videoFrame = document.getElementById('video-frame');
      const mobileVideoFrame = document.getElementById('mobile-video-frame');
      logMessage('收到视频流，正在设置...');
      console.log('视频流对象:', stream);
      console.log('视频轨道数量:', stream.getVideoTracks().length);
      
      // 确保视频元素属性正确设置
      const setupVideoElement = (element) => {
        if (element) {
          element.muted = true;
          element.autoplay = true;
          element.playsInline = true;
          element.controls = false;
          element.srcObject = stream;
        }
      };
      
      setupVideoElement(videoFrame);
      setupVideoElement(mobileVideoFrame);
      
      // 移动端兼容性：尝试播放视频
      const playVideo = (element) => {
        if (!element) return Promise.resolve();
        
        return element.play().then(() => {
          logMessage(`视频流已启动并开始播放 (${element.id})`);
          updateVideoDisplay(true);
          console.log('视频播放成功，视频尺寸:', element.videoWidth, 'x', element.videoHeight);
        }).catch((error) => {
          logMessage(`视频播放失败 (${element.id}): ${error.message}`);
          console.error('视频播放错误详情:', error);
          // 移动端可能需要用户交互才能播放
          if (error.name === 'NotAllowedError') {
            logMessage('移动端需要用户交互才能播放视频，请点击视频区域');
            // 添加点击事件监听器到整个视频容器
            const videoContainer = document.querySelector('.video-container');
            const clickHandler = () => {
              element.play().then(() => {
                logMessage('用户交互后视频开始播放');
                updateVideoDisplay(true);
                videoContainer.removeEventListener('click', clickHandler);
              }).catch(e => {
                logMessage(`用户交互后仍无法播放: ${e.message}`);
              });
            };
            if (videoContainer) {
              videoContainer.addEventListener('click', clickHandler);
              videoContainer.style.cursor = 'pointer';
            }
          }
          // 即使播放失败，也显示视频元素（可能是自动播放策略问题）
          updateVideoDisplay(true);
        });
      };
      
      // 延迟一点再尝试播放，给浏览器时间处理
      setTimeout(() => {
        playVideo(videoFrame);
        playVideo(mobileVideoFrame);
      }, 50); // 减少到50ms提高响应性
      
      // 监听视频元素的事件
      const addVideoEventListeners = (element) => {
        if (!element) return;
        
        element.addEventListener('loadedmetadata', () => {
          console.log(`视频元数据加载完成 (${element.id}):`, element.videoWidth, 'x', element.videoHeight);
        });
        
        element.addEventListener('canplay', () => {
          console.log(`视频可以播放 (${element.id})`);
        });
        
        element.addEventListener('playing', () => {
          console.log(`视频正在播放 (${element.id})`);
          updateVideoDisplay(true);
        });
        
        element.addEventListener('error', (e) => {
          console.error(`视频错误 (${element.id}):`, e);
        });
      };
      
      addVideoEventListeners(videoFrame);
      addVideoEventListeners(mobileVideoFrame);
    };

    // 添加音频轨道回调
    globalThis.rtc.onAudioTrack = (stream) => {
      logMessage('收到机器狗音频流');
      console.log('音频流对象:', stream);
      console.log('音频轨道数量:', stream.getAudioTracks().length);
      
      // 获取音频轨道详细信息
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length > 0) {
        const track = audioTracks[0];
        logMessage(`音频轨道: ${track.label || '未命名'}, 状态: ${track.readyState}`);
        console.log('音频轨道设置:', track.getSettings());
      }
      
      // 通知音频播放器有新的音频流可用
      if (window.robotAudioPlayer) {
        window.robotAudioPlayer.log('✅ 检测到机器狗音频流，可以开始播放');
        window.robotAudioPlayer.log(`📊 音频轨道: ${audioTracks.length}个, 状态: ${audioTracks[0]?.readyState || '未知'}`);
      }
    };
    
    globalThis.rtc.initSDP();
  } catch (error) {
    logMessage(`连接错误: ${error.message}`);
    updateConnectionStatus(false);
  }
}

function handleExecuteClick() {
  console.log('Execute button clicked');
  console.log('globalThis.rtc:', globalThis.rtc);
  if (globalThis.rtc) {
    console.log('rtc.connected:', globalThis.rtc.connected);
    console.log('rtc.channel:', globalThis.rtc.channel);
    console.log('rtc.channel.readyState:', globalThis.rtc.channel ? globalThis.rtc.channel.readyState : 'undefined');
    console.log('rtc.validationResult:', globalThis.rtc.validationResult);
  }
  
  if (!globalThis.rtc || !globalThis.rtc.connected) {
     logMessage('错误：未连接到机器人');
     return;
   }
   
   const command = parseInt(document.getElementById("command").value);
   
   if (!command) {
     logMessage('错误：请选择一个命令');
     return;
   }
  
  const uniqID =
    (new Date().valueOf() % 2147483648) + Math.floor(Math.random() * 1e3);

  console.log("Command:", command);
  logMessage(`执行命令: ${document.getElementById("command").selectedOptions[0].text}`);

  globalThis.rtc.publish("rt/api/sport/request", {
    header: { identity: { id: uniqID, api_id: command } },
    parameter: JSON.stringify(command),
    // api_id: command,
  });
}


function handleExecuteCustomClick() {
  if (!globalThis.rtc || !globalThis.rtc.connected) {
     logMessage('错误：未连接到机器人');
     return;
   }
   
   const command = document.getElementById("custom-command").value.trim();
   
   if (!command) {
     logMessage('错误：请输入自定义命令');
     return;
   }
  
  try {
    // Validate JSON if it looks like JSON
    if (command.startsWith('{') || command.startsWith('[')) {
      JSON.parse(command);
    }
    
    console.log("Custom Command:", command);
    logMessage(`发送自定义命令: ${command.substring(0, 50)}${command.length > 50 ? '...' : ''}`);
    
    globalThis.rtc.channel.send(command);
    
    // Clear the input after successful send
    document.getElementById("custom-command").value = '';
  } catch (error) {
    logMessage(`错误：无效的JSON格式 - ${error.message}`);
  }
}

function truncateString(str, maxLength) {
  if (typeof str !== "string") {
    str = JSON.stringify(str);
  }

  if (str.length > maxLength) {
    return str.substring(0, maxLength) + "...";
  } else {
    return str;
  }
}

function applyGamePadDeadzeone(value, th) {
  return Math.abs(value) > th ? value : 0
}

function joystickTick() {
  try {
    let x = 0, y = 0, z = 0;
    let gpToUse = document.getElementById("gamepad").value;
    
    if (gpToUse !== "NO") {
      const gamepads = navigator.getGamepads();
      let gp = gamepads[gpToUse];
      
      // LB must be pressed
      if (gp && gp.buttons && gp.buttons[4] && gp.buttons[4].pressed == true) {
        x = -1 * applyGamePadDeadzeone(gp.axes[1], 0.25);
        y = -1 * applyGamePadDeadzeone(gp.axes[2], 0.25);
        z = -1 * applyGamePadDeadzeone(gp.axes[0], 0.25);
      } 
    } else {
      // 检查虚拟摇杆是否存在（使用全局变量）
      if (!window.joyLeft || !window.joyRight) {
        console.warn('虚拟摇杆未初始化');
        return;
      }
      
      try {
        // 虚拟摇杆的中心位置是100，只有当摇杆偏离中心时才计算移动值
        const leftX = window.joyLeft.GetX(); // 获取标准化的X值 (-100 到 100)
        const leftY = window.joyLeft.GetY(); // 获取标准化的Y值 (-100 到 100)
        const rightX = window.joyRight.GetX(); // 获取标准化的X值 (-100 到 100)
        
        // 验证返回值是否有效
        if (isNaN(leftX) || isNaN(leftY) || isNaN(rightX)) {
          console.warn('虚拟摇杆返回无效值');
          return;
        }
        
        // 应用死区，避免微小的摇杆移动产生命令
        const deadzone = 10; // 10%的死区
        
        if (Math.abs(leftY) > deadzone) {
          x = -1 * leftY / 100; // 前进后退
        }
        if (Math.abs(rightX) > deadzone) {
          y = -1 * rightX / 100; // 左右移动
        }
        if (Math.abs(leftX) > deadzone) {
          z = -1 * leftX / 100; // 旋转
        }
      } catch (joystickError) {
        console.error('虚拟摇杆读取错误:', joystickError);
        return;
      }
    }

    // 防抖：检查命令是否有显著变化或时间间隔
    const now = Date.now();
    const commandChanged = Math.abs(x - lastControlCommand.x) > 0.02 || 
                          Math.abs(y - lastControlCommand.y) > 0.02 || 
                          Math.abs(z - lastControlCommand.z) > 0.02;
    const timeElapsed = now - lastCommandTime > COMMAND_THROTTLE_MS;
    
    // 只有当有实际移动时才发送命令
    if (Math.abs(x) < 0.01 && Math.abs(y) < 0.01 && Math.abs(z) < 0.01) {
      // 发送停止命令（如果之前有移动）
      if (Math.abs(lastControlCommand.x) > 0.01 || Math.abs(lastControlCommand.y) > 0.01 || Math.abs(lastControlCommand.z) > 0.01) {
        lastControlCommand = { x: 0, y: 0, z: 0 };
        lastCommandTime = now;
        if(globalThis.rtc) {
          globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify({x: 0, y: 0, z: 0}));
          console.log("发送停止命令");
        }
      }
      return;
    }

    if (x == undefined || y == undefined || z == undefined) {
      return;
    }

    // 防抖：只有命令变化显著或时间间隔足够时才发送
    if (!commandChanged && !timeElapsed) {
      return;
    }

    console.log("Joystick Linear:", x, y, z);

    if(globalThis.rtc == undefined) {
      console.warn('RTC连接未建立');
      return;
    }
    
    // 更新最后发送的命令和时间
    lastControlCommand = { x, y, z };
    lastCommandTime = now;
    
    globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify({x: x, y: y, z: z}));
  } catch (error) {
    console.error('joystickTick函数错误:', error);
  }
}

function addJoysticks() {
  try {
    // 检查虚拟摇杆容器是否存在
    const leftContainer = document.getElementById('joy-left');
    const rightContainer = document.getElementById('joy-right');
    
    if (!leftContainer || !rightContainer) {
      console.warn('虚拟摇杆容器未找到');
      return;
    }
    
    console.log('初始化虚拟摇杆...');
    
    const joyConfig = {
      internalFillColor: "#FFFFFF",
      internalLineWidth: 2,
      internalStrokeColor: "rgba(240, 240, 240, 0.3)",
      externalLineWidth: 1,
      externalStrokeColor: "#FFFFFF",
      autoReturnToCenter: true
    };
    
    // 创建摇杆实例并存储在全局变量中
    window.joyLeft = new JoyStick("joy-left", joyConfig);
    window.joyRight = new JoyStick("joy-right", joyConfig);
    
    // 验证摇杆是否成功创建
    if (!window.joyLeft || !window.joyRight) {
      console.error('虚拟摇杆创建失败');
      return;
    }
    
    console.log('虚拟摇杆初始化成功');
    
    // 使用更安全的定时器
    const joystickInterval = setInterval(() => {
      try {
        joystickTick();
      } catch (error) {
        console.error('虚拟摇杆定时器错误:', error);
        clearInterval(joystickInterval);
      }
    }, 50); // 减少到50ms提高响应性
    
    // 存储定时器ID以便后续清理
    window.joystickInterval = joystickInterval;
    
  } catch (error) {
    console.error('addJoysticks函数错误:', error);
  }
}

const buildGamePadsSelect = (e) => {
  const gp = navigator.getGamepads().filter(x => x != null && x.id.toLowerCase().indexOf("xbox") != -1);

  const gamepadSelect = document.getElementById("gamepad");
  gamepadSelect.innerHTML = "";

  const option = document.createElement("option");
  option.value = "NO";
  option.textContent = "不使用手柄"
  option.selected = true;
  gamepadSelect.appendChild(option);  

  Object.entries(gp).forEach(([index, value]) => {
    if (!value) return
    const option = document.createElement("option");
    option.value = value.index;
    option.textContent = value.id;
    gamepadSelect.appendChild(option);
  });
};

window.addEventListener("gamepadconnected", buildGamePadsSelect);
window.addEventListener("gamepaddisconnected", buildGamePadsSelect);
buildGamePadsSelect();

// Load saved values when the page loads
document.addEventListener("DOMContentLoaded", loadSavedValues);
document.addEventListener("DOMContentLoaded", addJoysticks);
document.addEventListener("DOMContentLoaded", addMobileTouchSupport);

// 检测移动设备
function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
}

// 操控模式管理
let currentControlMode = 'desktop'; // 'desktop' 或 'mobile'
let mobileJoysticksInitialized = false;

// 显示操控模式选择弹窗
function showControlModeModal() {
  const modal = document.getElementById('control-mode-modal');
  if (modal) {
    modal.style.display = 'flex';
  }
}

// 隐藏操控模式选择弹窗
function hideControlModeModal() {
  const modal = document.getElementById('control-mode-modal');
  if (modal) {
    modal.style.display = 'none';
  }
}

// 切换到桌面操控模式
function switchToDesktopMode() {
  currentControlMode = 'desktop';
  
  // 隐藏手机操控布局
  const mobileLayout = document.getElementById('mobile-control-layout');
  if (mobileLayout) {
    mobileLayout.style.display = 'none';
  }
  
  // 显示原有的页面布局
  const mainContainer = document.querySelector('.main-container');
  const header = document.querySelector('.header');
  const customCommand = document.querySelector('.custom-command');
  const logContainer = document.querySelector('.log-container');
  
  if (mainContainer) mainContainer.style.display = 'block';
  if (header) header.style.display = 'block';
  if (customCommand) customCommand.style.display = 'block';
  if (logContainer) logContainer.style.display = 'block';
  
  hideControlModeModal();
  console.log('已切换到桌面操控模式');
}

// 切换到手机操控模式
function switchToMobileMode() {
  currentControlMode = 'mobile';
  
  // 隐藏原有的页面布局
  const mainContainer = document.querySelector('.main-container');
  const header = document.querySelector('.header');
  const customCommand = document.querySelector('.custom-command');
  const logContainer = document.querySelector('.log-container');
  
  if (mainContainer) mainContainer.style.display = 'none';
  if (header) header.style.display = 'none';
  if (customCommand) customCommand.style.display = 'none';
  if (logContainer) logContainer.style.display = 'none';
  
  // 显示手机操控布局
  const mobileLayout = document.getElementById('mobile-control-layout');
  if (mobileLayout) {
    mobileLayout.style.display = 'flex';
  }
  
  // 同步视频流
  syncVideoStreams();
  
  // 初始化手机摇杆
  if (!mobileJoysticksInitialized) {
    initializeMobileJoysticks();
    mobileJoysticksInitialized = true;
  }
  
  hideControlModeModal();
  console.log('已切换到手机操控模式');
}

// 同步视频流
function syncVideoStreams() {
  const originalVideo = document.getElementById('video-frame');
  const mobileVideo = document.getElementById('mobile-video-frame');
  
  if (originalVideo && mobileVideo) {
    // 复制视频源
    mobileVideo.srcObject = originalVideo.srcObject;
    mobileVideo.src = originalVideo.src;
  }
}

// 初始化手机摇杆
function initializeMobileJoysticks() {
  try {
    console.log('开始初始化手机虚拟摇杆');
    
    // 摇杆配置
    const joyConfig = {
      title: "joystick",
      width: 160,
      height: 160,
      internalFillColor: "rgba(0, 212, 255, 0.2)",
      internalLineWidth: 3,
      internalStrokeColor: "#00d4ff",
      externalLineWidth: 3,
      externalStrokeColor: "rgba(255, 255, 255, 0.3)",
      autoReturnToCenter: true
    };
    
    // 右摇杆配置（不同颜色）
    const rightJoyConfig = {
      title: "joystick",
      width: 160,
      height: 160,
      internalFillColor: "rgba(255, 107, 107, 0.2)",
      internalLineWidth: 3,
      internalStrokeColor: "#ff6b6b",
      externalLineWidth: 3,
      externalStrokeColor: "rgba(255, 255, 255, 0.3)",
      autoReturnToCenter: true
    };
    
    // 创建手机摇杆实例 - 使用专门的移动端摇杆库
    // 左摇杆回调函数
    const leftJoyCallback = function(stickStatus) {
      // 更新左摇杆状态，用于mobileJoystickTick函数读取
      if (window.mobileJoyLeft) {
        window.mobileJoyLeft._lastStatus = stickStatus;
      }
    };
    
    // 右摇杆回调函数
    const rightJoyCallback = function(stickStatus) {
      // 更新右摇杆状态，用于mobileJoystickTick函数读取
      if (window.mobileJoyRight) {
        window.mobileJoyRight._lastStatus = stickStatus;
      }
    };
    
    window.mobileJoyLeft = new MobileJoyStick("mobile-joy-left", joyConfig, leftJoyCallback);
    window.mobileJoyRight = new MobileJoyStick("mobile-joy-right", rightJoyConfig, rightJoyCallback);
    
    if (window.mobileJoyLeft && window.mobileJoyRight) {
      console.log('手机虚拟摇杆初始化成功');
      
      // 启动摇杆更新循环
      if (window.mobileJoystickInterval) {
        clearInterval(window.mobileJoystickInterval);
      }
      
      window.mobileJoystickInterval = setInterval(() => {
        try {
          mobileJoystickTick();
        } catch (error) {
          console.error('手机摇杆更新出错:', error);
        }
      }, 50); // 减少到50ms提高响应性
      
    }
  } catch (error) {
    console.error('手机摇杆初始化失败:', error);
  }
}

// 速度控制滑动条事件监听器
function initializeSpeedControl() {
  const speedSlider = document.getElementById('speed-slider');
  const speedValue = document.getElementById('speed-value');
  
  if (speedSlider && speedValue) {
    // 初始化显示
    speedValue.textContent = speedSlider.value + '%';
    globalThis.speedMultiplier = parseInt(speedSlider.value) / 100;
    
    // 监听滑动条变化
    speedSlider.addEventListener('input', function() {
      const speed = parseInt(this.value);
      speedValue.textContent = speed + '%';
      globalThis.speedMultiplier = speed / 100;
      console.log('🎛️ 速度调节:', speed + '%', '倍数:', globalThis.speedMultiplier.toFixed(2));
    });
    
    // 监听滑动条释放事件，提供触觉反馈
    speedSlider.addEventListener('change', function() {
      const speed = parseInt(this.value);
      console.log('✅ 速度设置完成:', speed + '%');
      
      // 简单的触觉反馈（如果设备支持）
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    });
  }
}

// 手机摇杆更新函数
function mobileJoystickTick() {
  try {
    let x = 0, y = 0, z = 0;
    
    // 检查手机摇杆是否存在
    if (!window.mobileJoyLeft || !window.mobileJoyRight) {
      console.warn('手机虚拟摇杆未初始化');
      return;
    }
    
    try {
      // 获取摇杆输入值 (-100 到 100)
      // 使用回调函数更新的状态数据
      const leftStatus = window.mobileJoyLeft._lastStatus || { x: 0, y: 0 };
      const rightStatus = window.mobileJoyRight._lastStatus || { x: 0, y: 0 };
      
      const leftX = parseInt(leftStatus.x) || 0;  // 左摇杆X轴 (左右横移)
      const leftY = parseInt(leftStatus.y) || 0;  // 左摇杆Y轴 (前进后退)
      const rightX = parseInt(rightStatus.x) || 0; // 右摇杆X轴 (左右转向)
      const rightY = parseInt(rightStatus.y) || 0; // 右摇杆Y轴 (暂不使用)
      
      // 验证返回值是否有效
      if (isNaN(leftX) || isNaN(leftY) || isNaN(rightX)) {
        console.warn('❌ 手机虚拟摇杆返回无效值');
        return;
      }
      
      // 显示原始摇杆数值（用于调试）
      if (Math.abs(leftX) > 10 || Math.abs(leftY) > 10 || Math.abs(rightX) > 10) {
        console.log('📱 摇杆原始值 - 左摇杆(X,Y):', leftX, leftY, '右摇杆(X,Y):', rightX, rightY);
      }
      
      // 应用死区，避免微小的摇杆移动产生命令
      const deadzone = 10; // 10%的死区
      
      // 左摇杆Y轴控制前进后退 (JoyStick库已经处理了Y轴反转)
      if (Math.abs(leftY) > deadzone) {
        x = (leftY / 100) * globalThis.speedMultiplier; // 前进后退，应用速度倍数
      }
      
      // 左摇杆X轴控制左右横移
      if (Math.abs(leftX) > deadzone) {
        y = (-1 * leftX / 100) * globalThis.speedMultiplier; // 左右横移，应用速度倍数
      }
      
      // 右摇杆X轴控制左右转向（旋转）
      if (Math.abs(rightX) > deadzone) {
        z = (-1 * rightX / 100) * globalThis.speedMultiplier; // 左右转向，应用速度倍数
      }
      
    } catch (joystickError) {
      console.error('手机虚拟摇杆读取错误:', joystickError);
      return;
    }
    
    // 防抖：检查命令是否有显著变化或时间间隔
    const now = Date.now();
    const commandChanged = Math.abs(x - lastMobileControlCommand.x) > 0.02 || 
                          Math.abs(y - lastMobileControlCommand.y) > 0.02 || 
                          Math.abs(z - lastMobileControlCommand.z) > 0.02;
    const timeElapsed = now - lastMobileCommandTime > COMMAND_THROTTLE_MS;
    
    // 限制控制值范围，防止异常值
    x = Math.max(-1, Math.min(1, x));
    y = Math.max(-1, Math.min(1, y));
    z = Math.max(-1, Math.min(1, z));
    
    // 只有当有实际移动时才发送命令
    if (Math.abs(x) < 0.01 && Math.abs(y) < 0.01 && Math.abs(z) < 0.01) {
      // 发送停止命令（如果之前有移动）
      if (Math.abs(lastMobileControlCommand.x) > 0.01 || Math.abs(lastMobileControlCommand.y) > 0.01 || Math.abs(lastMobileControlCommand.z) > 0.01) {
        lastMobileControlCommand = { x: 0, y: 0, z: 0 };
        lastMobileCommandTime = now;
        if(globalThis.rtc && globalThis.rtc.dataChannel && globalThis.rtc.dataChannel.readyState === 'open') {
          try {
            globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify({x: 0, y: 0, z: 0}));
            console.log("手机摇杆发送停止命令");
          } catch (error) {
            console.error('发送停止命令失败:', error);
          }
        }
      }
      return;
    }
    
    if (x == undefined || y == undefined || z == undefined || isNaN(x) || isNaN(y) || isNaN(z)) {
      console.warn('控制值无效，跳过发送');
      return;
    }
    
    // 防抖：只有命令变化显著或时间间隔足够时才发送
    if (!commandChanged && !timeElapsed) {
      return;
    }
    
    // 详细的调试信息
    console.log("🎮 手机摇杆控制 - X(前后):", x.toFixed(3), "Y(横移):", y.toFixed(3), "Z(转向):", z.toFixed(3));
    
    if(globalThis.rtc == undefined) {
      console.warn('❌ RTC连接未建立，无法发送控制命令');
      return;
    }
    
    // 检查数据通道状态
    if(globalThis.rtc.channel && globalThis.rtc.channel.readyState !== 'open') {
      console.warn('❌ 数据通道未打开，状态:', globalThis.rtc.channel.readyState);
      return;
    }
    
    // 再次验证控制值的有效性
    if (Math.abs(x) > 1 || Math.abs(y) > 1 || Math.abs(z) > 1) {
      console.warn('❌ 控制值超出范围，跳过发送:', { x, y, z });
      return;
    }
    
    // 更新最后发送的命令和时间
    lastMobileControlCommand = { x, y, z };
    lastMobileCommandTime = now;
    
    try {
      // 使用与原有摇杆相同的API调用方式
      const commandData = {x: parseFloat(x.toFixed(3)), y: parseFloat(y.toFixed(3)), z: parseFloat(z.toFixed(3))};
      globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify(commandData));
      console.log("✅ 控制命令已发送:", commandData);
    } catch (sendError) {
      console.error('❌ 发送控制命令失败:', sendError);
    }
    
  } catch (error) {
    console.error('手机摇杆更新错误:', error);
  }
}

// 页面加载完成后初始化操控模式功能
document.addEventListener('DOMContentLoaded', function() {
  // 初始化速度控制
  initializeSpeedControl();
  // 移动端视频播放优化
  if (isMobileDevice()) {
    const video = document.getElementById('video-frame');
    if (video) {
      video.setAttribute('playsinline', 'true');
      video.setAttribute('webkit-playsinline', 'true');
      video.muted = true;
      console.log('移动端视频播放优化已应用');
    }
  }
  
  // 操控模式按钮事件监听器
  const controlModeBtn = document.getElementById('control-mode-btn');
  if (controlModeBtn) {
    controlModeBtn.addEventListener('click', showControlModeModal);
    controlModeBtn.addEventListener('touchstart', function(e) {
      e.preventDefault();
      showControlModeModal();
    }, { passive: false });
  }
  
  // 模式选择按钮事件监听器
  const desktopModeBtn = document.getElementById('desktop-mode-btn');
  const mobileModeBtn = document.getElementById('mobile-mode-btn');
  const closeModeBtn = document.getElementById('close-modal-btn');
  const exitMobileBtn = document.getElementById('exit-mobile-mode');
  
  if (desktopModeBtn) {
    desktopModeBtn.addEventListener('click', switchToDesktopMode);
  }
  
  if (mobileModeBtn) {
    mobileModeBtn.addEventListener('click', switchToMobileMode);
  }
  
  if (closeModeBtn) {
    closeModeBtn.addEventListener('click', hideControlModeModal);
  }
  
  if (exitMobileBtn) {
    exitMobileBtn.addEventListener('click', switchToDesktopMode);
  }
  
  // 点击弹窗背景关闭弹窗
  const modal = document.getElementById('control-mode-modal');
  if (modal) {
    modal.addEventListener('click', function(e) {
      if (e.target === modal) {
        hideControlModeModal();
      }
    });
  }
  
  console.log('操控模式功能初始化完成');
});

document.getElementById("gamepad").addEventListener("change", () => {
//alert("change");
});

// Attach event listener to connect button
document
  .getElementById("connect-btn")
  .addEventListener("click", handleConnectClick);

document
  .getElementById("execute-btn")
  .addEventListener("click", handleExecuteClick);

document
  .getElementById("execute-custom-btn")
  .addEventListener("click", handleExecuteCustomClick);




  document.addEventListener('keydown', function(event) {
    const key = event.key.toLowerCase();
    let x = 0, y = 0, z = 0;

    switch (key) {
        case 'w': // Forward
            x = 0.8;
            break;
        case 's': // Reverse
            x = -0.4;
            break;
        case 'a': // Sideways left
            y = 0.4;
            break;
        case 'd': // Sideways right
            y = -0.4;
            break;
        case 'q': // Turn left
            z = 2;
            break;
        case 'e': // Turn right
            z = -2;
            break;
        default:
            return; // Ignore other keys
    }

    if(globalThis.rtc !== undefined) {
        globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify({x: x, y: y, z: z}));
    }
});

document.addEventListener('keyup', function(event) {
    const key = event.key.toLowerCase();
    if (key === 'w' || key === 's' || key === 'a' || key === 'd' || key === 'q' || key === 'e') {
        if(globalThis.rtc !== undefined) {
            // Stop movement by sending zero velocity
            globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify({x: 0, y: 0, z: 0}));
        }
    }
});





document.addEventListener("DOMContentLoaded", function() {
  // 初始化事件监听
  const connectButton = document.getElementById('connect-btn');
  if (connectButton) {
    connectButton.addEventListener('click', handleConnectClick);
  }
  
  const executeButton = document.getElementById('execute-btn');
  if (executeButton) {
    executeButton.addEventListener('click', handleExecuteClick);
  }
  
  const executeCustomButton = document.getElementById('execute-custom-btn');
  if (executeCustomButton) {
    executeCustomButton.addEventListener('click', handleExecuteCustomClick);
  }
  
  // 加载保存的配置
  loadSavedValues();
  
  // 移动端支持
  addMobileTouchSupport();
  
  // 检测是否为移动设备
  if (isMobileDevice()) {
    document.body.classList.add('mobile-device');
  }
  
  // 初始化标准键盘控制
  addJoysticks();
  
  // 初始化速度控制
  initializeSpeedControl();
  
  // 使用Resize Observer实现更好的视频尺寸调整
  const videoContainer = document.querySelector('.video-container');
  if (videoContainer) {
    new ResizeObserver(syncVideoStreams).observe(videoContainer);
  }
  
  // 初始化控制模式切换事件
  document.getElementById('control-mode-button')?.addEventListener('click', showControlModeModal);
  document.getElementById('desktop-mode-btn')?.addEventListener('click', switchToDesktopMode);
  document.getElementById('mobile-mode-btn')?.addEventListener('click', switchToMobileMode);
  
  // 在PC模式下初始化
  if (localStorage.getItem('controlMode') === 'mobile') {
    switchToMobileMode();
  } else {
    switchToDesktopMode();
  }
});

