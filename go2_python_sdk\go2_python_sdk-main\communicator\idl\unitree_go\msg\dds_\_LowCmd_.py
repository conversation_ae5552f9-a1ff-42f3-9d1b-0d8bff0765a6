"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.10.2
  Module: unitree_go.msg.dds_
  IDL file: LowCmd_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
import unitree_go


@dataclass
@annotate.final
@annotate.autoid("sequential")
class LowCmd_(idl.IdlStruct, typename="unitree_go.msg.dds_.LowCmd_"):
    head: types.array[types.uint8, 2]
    level_flag: types.uint8
    frame_reserve: types.uint8
    sn: types.array[types.uint32, 2]
    version: types.array[types.uint32, 2]
    bandwidth: types.uint16
    motor_cmd: types.array['unitree_go.msg.dds_.MotorCmd_', 20]
    bms_cmd: 'unitree_go.msg.dds_.BmsCmd_'
    wireless_remote: types.array[types.uint8, 40]
    led: types.array[types.uint8, 12]
    fan: types.array[types.uint8, 2]
    gpio: types.uint8
    reserve: types.uint32
    crc: types.uint32


