/**
 * Canvas视频流处理类
 * 用于从WebRTC视频流中提取帧数据并通过WebSocket发送到中继服务器
 */

class CanvasStreamer {
    constructor(websocketUrl, options = {}) {
        this.websocketUrl = websocketUrl;
        this.socket = null;
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.isStreaming = false;
        this.frameRate = options.frameRate || 30; // 目标帧率
        this.quality = options.quality || 0.8; // JPEG质量 (0.1-1.0)
        this.maxWidth = options.maxWidth || 1280; // 最大宽度
        this.maxHeight = options.maxHeight || 720; // 最大高度
        this.roomId = options.roomId || 'default';
        
        // 性能监控
        this.stats = {
            framesSent: 0,
            framesDropped: 0,
            avgFrameSize: 0,
            lastFrameTime: 0,
            connectionState: 'disconnected'
        };
        
        // 帧率控制
        this.frameInterval = 1000 / this.frameRate;
        this.lastFrameTime = 0;
        
        // 连接状态回调
        this.onConnectionStateChange = null;
        this.onStatsUpdate = null;
        this.onError = null;
        
        this.initCanvas();
    }
    
    initCanvas() {
        // 设置canvas样式（用于调试）
        this.canvas.style.display = 'none';
        document.body.appendChild(this.canvas);
    }
    
    connect() {
        return new Promise((resolve, reject) => {
            try {
                // 使用Socket.IO客户端，配置传输选项
                this.socket = io(this.websocketUrl, {
                    transports: ['websocket', 'polling'],
                    upgrade: false,
                    rememberUpgrade: false,
                    timeout: 20000,
                    forceNew: true,
                    withCredentials: true
                });
                
                this.socket.on('connect', () => {
                    console.log('Socket.IO连接已建立');
                    this.stats.connectionState = 'connected';
                    this.updateConnectionState('connected');
                    
                    // 加入房间作为推流者
                    this.socket.emit('join_as_streamer', {
                        room_id: this.roomId
                    });
                    
                    resolve();
                });
                
                this.socket.on('message', (data) => {
                    this.handleMessage(data);
                });
                
                this.socket.on('disconnect', () => {
                    console.log('Socket.IO连接已关闭');
                    this.stats.connectionState = 'disconnected';
                    this.updateConnectionState('disconnected');
                    this.isStreaming = false;
                });
                
                this.socket.on('connect_error', (error) => {
                    console.error('Socket.IO连接错误:', error);
                    this.stats.connectionState = 'error';
                    this.updateConnectionState('error');
                    if (this.onError) this.onError(error);
                    reject(error);
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'streamer_joined':
                console.log('成功加入房间作为推流者:', message.room_id);
                break;
            case 'error':
                console.error('服务器错误:', message.message);
                if (this.onError) this.onError(new Error(message.message));
                break;
            case 'stats_update':
                if (this.onStatsUpdate) this.onStatsUpdate(message.stats);
                break;
        }
    }
    
    updateConnectionState(state) {
        if (this.onConnectionStateChange) {
            this.onConnectionStateChange(state);
        }
    }
    
    startStreaming(videoElement) {
        if (!videoElement) {
            throw new Error('视频元素不能为空');
        }
        
        if (!this.socket || !this.socket.connected) {
            throw new Error('Socket.IO未连接');
        }
        
        this.isStreaming = true;
        console.log('开始视频流传输...');
        
        const captureFrame = (timestamp) => {
            if (!this.isStreaming) return;
            
            // 帧率控制
            if (timestamp - this.lastFrameTime < this.frameInterval) {
                requestAnimationFrame(captureFrame);
                return;
            }
            
            try {
                this.captureAndSendFrame(videoElement, timestamp);
                this.lastFrameTime = timestamp;
            } catch (error) {
                console.error('帧捕获错误:', error);
                this.stats.framesDropped++;
            }
            
            requestAnimationFrame(captureFrame);
        };
        
        requestAnimationFrame(captureFrame);
    }
    
    captureAndSendFrame(videoElement, timestamp) {
        // 检查视频是否准备就绪
        if (videoElement.readyState < 2) {
            return; // 视频未准备好
        }
        
        const videoWidth = videoElement.videoWidth;
        const videoHeight = videoElement.videoHeight;
        
        if (videoWidth === 0 || videoHeight === 0) {
            return; // 无效的视频尺寸
        }
        
        // 计算缩放后的尺寸（保持宽高比）
        const scale = Math.min(
            this.maxWidth / videoWidth,
            this.maxHeight / videoHeight,
            1 // 不放大
        );
        
        const scaledWidth = Math.floor(videoWidth * scale);
        const scaledHeight = Math.floor(videoHeight * scale);
        
        // 设置canvas尺寸
        this.canvas.width = scaledWidth;
        this.canvas.height = scaledHeight;
        
        // 绘制视频帧
        this.ctx.drawImage(videoElement, 0, 0, scaledWidth, scaledHeight);
        
        // 转换为JPEG并发送
        this.canvas.toBlob((blob) => {
            if (blob && this.socket?.connected) {
                this.sendFrame(blob, timestamp);
            }
        }, 'image/jpeg', this.quality);
    }
    
    sendFrame(blob, timestamp) {
        const reader = new FileReader();
        reader.onload = () => {
            const base64Data = reader.result.split(',')[1]; // 移除data:image/jpeg;base64,前缀
            
            const frameData = {
                type: 'video_frame',
                room_id: this.roomId,
                frame_data: base64Data,
                timestamp: timestamp,
                size: blob.size,
                width: this.canvas.width,
                height: this.canvas.height
            };
            
            try {
                this.socket.emit('video_frame', frameData);
                this.stats.framesSent++;
                this.updateAvgFrameSize(blob.size);
            } catch (error) {
                console.error('发送帧数据失败:', error);
                this.stats.framesDropped++;
            }
        };
        
        reader.readAsDataURL(blob);
    }
    
    updateAvgFrameSize(size) {
        // 使用指数移动平均计算平均帧大小
        const alpha = 0.1;
        this.stats.avgFrameSize = this.stats.avgFrameSize * (1 - alpha) + size * alpha;
    }
    
    stopStreaming() {
        this.isStreaming = false;
        console.log('停止视频流传输');
    }
    
    disconnect() {
        this.stopStreaming();
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
    }
    
    getStats() {
        return {
            ...this.stats,
            frameRate: this.stats.framesSent > 0 ? 
                this.stats.framesSent / ((Date.now() - this.stats.lastFrameTime) / 1000) : 0
        };
    }
    
    // 动态调整质量和帧率
    adjustQuality(newQuality) {
        this.quality = Math.max(0.1, Math.min(1.0, newQuality));
        console.log('调整视频质量为:', this.quality);
    }
    
    adjustFrameRate(newFrameRate) {
        this.frameRate = Math.max(1, Math.min(60, newFrameRate));
        this.frameInterval = 1000 / this.frameRate;
        console.log('调整帧率为:', this.frameRate);
    }
    
    // 自适应质量调整（基于网络状况）
    enableAdaptiveQuality() {
        setInterval(() => {
            const dropRate = this.stats.framesDropped / (this.stats.framesSent + this.stats.framesDropped);
            
            if (dropRate > 0.1) { // 丢帧率超过10%
                this.adjustQuality(this.quality * 0.9); // 降低质量
                this.adjustFrameRate(this.frameRate * 0.9); // 降低帧率
            } else if (dropRate < 0.02 && this.quality < 0.9) { // 丢帧率低于2%且质量不是最高
                this.adjustQuality(this.quality * 1.05); // 提高质量
            }
        }, 5000); // 每5秒检查一次
    }
}

// 视频观看器类
class CanvasViewer {
    constructor(websocketUrl, options = {}) {
        this.websocketUrl = websocketUrl;
        this.socket = null;
        this.roomId = options.roomId || 'default';
        this.targetElement = null;
        
        // 性能统计
        this.stats = {
            framesReceived: 0,
            framesDisplayed: 0,
            avgLatency: 0,
            connectionState: 'disconnected'
        };
        
        // 回调函数
        this.onConnectionStateChange = null;
        this.onFrameReceived = null;
        this.onStatsUpdate = null;
        this.onError = null;
    }
    
    connect() {
        return new Promise((resolve, reject) => {
            try {
                // 使用Socket.IO客户端，配置传输选项
                this.socket = io(this.websocketUrl, {
                    transports: ['websocket', 'polling'],
                    upgrade: false,
                    rememberUpgrade: false,
                    timeout: 20000,
                    forceNew: true,
                    withCredentials: true
                });
                
                this.socket.on('connect', () => {
                    console.log('观看端Socket.IO连接已建立');
                    this.stats.connectionState = 'connected';
                    this.updateConnectionState('connected');
                    
                    // 加入房间作为观看者
                    this.socket.emit('join_as_viewer', {
                        room_id: this.roomId
                    });
                    
                    resolve();
                });
                
                this.socket.on('message', (data) => {
                    this.handleMessage(data);
                });
                
                this.socket.on('video_frame', (data) => {
                    this.handleMessage(data);
                });
                
                this.socket.on('disconnect', () => {
                    console.log('观看端Socket.IO连接已关闭');
                    this.stats.connectionState = 'disconnected';
                    this.updateConnectionState('disconnected');
                });
                
                this.socket.on('connect_error', (error) => {
                    console.error('观看端Socket.IO连接错误:', error);
                    this.stats.connectionState = 'error';
                    this.updateConnectionState('error');
                    if (this.onError) this.onError(error);
                    reject(error);
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    handleMessage(message) {
        switch (message.type) {
            case 'viewer_joined':
                console.log('成功加入房间作为观看者:', message.room_id);
                // 如果有缓存帧，立即显示
                if (message.cached_frame) {
                    this.displayFrame(message.cached_frame);
                }
                break;
            case 'video_frame':
                this.handleVideoFrame(message);
                break;
            case 'error':
                console.error('服务器错误:', message.message);
                if (this.onError) this.onError(new Error(message.message));
                break;
            case 'stats_update':
                if (this.onStatsUpdate) this.onStatsUpdate(message.stats);
                break;
        }
    }
    
    handleVideoFrame(frameData) {
        this.stats.framesReceived++;
        
        // 计算延迟
        const currentTime = Date.now();
        const latency = currentTime - frameData.timestamp;
        this.updateAvgLatency(latency);
        
        this.displayFrame(frameData);
        
        if (this.onFrameReceived) {
            this.onFrameReceived(frameData, latency);
        }
    }
    
    displayFrame(frameData) {
        if (!this.targetElement) return;
        
        const img = new Image();
        img.onload = () => {
            if (this.targetElement.tagName === 'CANVAS') {
                const ctx = this.targetElement.getContext('2d');
                this.targetElement.width = img.width;
                this.targetElement.height = img.height;
                ctx.drawImage(img, 0, 0);
            } else {
                this.targetElement.src = img.src;
            }
            this.stats.framesDisplayed++;
        };
        
        img.src = `data:image/jpeg;base64,${frameData.frame_data}`;
    }
    
    setTargetElement(element) {
        this.targetElement = element;
    }
    
    updateConnectionState(state) {
        if (this.onConnectionStateChange) {
            this.onConnectionStateChange(state);
        }
    }
    
    updateAvgLatency(latency) {
        const alpha = 0.1;
        this.stats.avgLatency = this.stats.avgLatency * (1 - alpha) + latency * alpha;
    }
    
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
    }
    
    getStats() {
        return { ...this.stats };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CanvasStreamer, CanvasViewer };
} else {
    window.CanvasStreamer = CanvasStreamer;
    window.CanvasViewer = CanvasViewer;
}

// ES6模块导出
export { CanvasStreamer, CanvasViewer };