"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.10.2
  Module: unitree_go.msg.dds_
  IDL file: WirelessController_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
import unitree_go


@dataclass
@annotate.final
@annotate.autoid("sequential")
class WirelessController_(idl.IdlStruct, typename="unitree_go.msg.dds_.WirelessController_"):
    lx: types.float32
    ly: types.float32
    rx: types.float32
    ry: types.float32
    keys: types.uint16


