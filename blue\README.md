# 蓝牙超声波音频控制器

这是一个在树莓派上运行的Python程序，用于控制蓝牙音频设备播放超声波频率，实现从低赫兹到高赫兹的音频播放功能。

## 功能特性

- **频率控制**: 支持1Hz-22kHz的频率范围
- **音量控制**: 0-100%音量调节
- **播放控制**: 播放/暂停/恢复功能
- **蓝牙管理**: 连接/断开蓝牙音频设备
- **API接口**: RESTful API用于远程控制
- **GUI界面**: 简单易用的图形界面
- **超声波生成**: 专门用于超声波频率播放

## 系统要求

- 树莓派 (推荐4B或更新版本)
- Raspberry Pi OS
- Python 3.7+
- 蓝牙适配器
- 音频输出设备

## 安装步骤

### 1. 克隆或下载项目文件

将以下文件放在同一目录中：
- `bluetooth_ultrasonic_controller.py` - 主控制程序
- `gui_controller.py` - GUI界面
- `test_api.py` - API测试脚本
- `requirements.txt` - Python依赖
- `install_setup.sh` - 安装脚本

### 2. 运行安装脚本

```bash
chmod +x install_setup.sh
sudo ./install_setup.sh
```

### 3. 重启系统

```bash
sudo reboot
```

## 使用方法

### 启动API服务器

```bash
python3 bluetooth_ultrasonic_controller.py
```

服务器将在 `http://localhost:5000` 启动

**API文档**: 访问 `http://localhost:5000/docs/` 查看完整的Swagger API文档

### 启动GUI界面

```bash
python3 gui_controller.py
```

### 测试API功能

```bash
python3 test_api.py
```

## API接口文档

### 基础URL
```
http://localhost:5000/api
```

### 播放控制

#### 开始播放
```http
POST /api/play
Content-Type: application/json

{
    "frequency": 1000
}
```

#### 暂停播放
```http
POST /api/pause
```

#### 恢复播放
```http
POST /api/resume
```

### 音频设置

#### 设置音量
```http
POST /api/volume
Content-Type: application/json

{
    "volume": 0.5
}
```

#### 设置频率
```http
POST /api/frequency
Content-Type: application/json

{
    "frequency": 10000
}
```

### 蓝牙设备管理

#### 获取设备列表
```http
GET /api/bluetooth/devices
```

#### 连接设备
```http
POST /api/bluetooth/connect
Content-Type: application/json

{
    "address": "AA:BB:CC:DD:EE:FF"
}
```

#### 断开设备
```http
POST /api/bluetooth/disconnect
Content-Type: application/json

{
    "address": "AA:BB:CC:DD:EE:FF"
}
```

### 状态查询

#### 获取当前状态
```http
GET /api/status
```

响应示例：
```json
{
    "status": "success",
    "data": {
        "is_playing": false,
        "frequency": 1000,
        "volume": 0.5,
        "connected_device": null,
        "sample_rate": 44100
    }
}
```

## GUI界面使用

1. **频率控制**
   - 手动输入频率值
   - 使用预设频率按钮（100Hz, 1kHz, 10kHz, 20kHz）

2. **音量控制**
   - 拖动滑块调节音量

3. **播放控制**
   - 播放/停止按钮
   - 暂停/恢复按钮

4. **蓝牙设备**
   - 刷新设备列表
   - 连接/断开设备

5. **状态显示**
   - 实时显示操作日志和状态信息

## 超声波频率说明

- **低频范围**: 1-100Hz (次声波)
- **可听范围**: 100-20,000Hz
- **超声波范围**: 20,000Hz以上

注意：人耳听觉范围通常为20Hz-20kHz，超过此范围的频率可能无法听到，但设备仍会播放。

## 故障排除

### 1. 音频播放问题
```bash
# 检查音频设备
aplay -l

# 检查PulseAudio状态
pulseaudio --check -v
```

### 2. 蓝牙连接问题
```bash
# 检查蓝牙服务
sudo systemctl status bluetooth

# 手动扫描设备
bluetoothctl scan on
```

### 3. 权限问题
```bash
# 添加用户到音频组
sudo usermod -a -G audio $USER

# 添加用户到蓝牙组
sudo usermod -a -G bluetooth $USER
```

### 4. 依赖问题
```bash
# 重新安装Python依赖
pip3 install -r requirements.txt --force-reinstall
```

## 开发说明

### 项目结构
```
├── bluetooth_ultrasonic_controller.py  # 主控制程序
├── gui_controller.py                   # GUI界面
├── test_api.py                        # API测试
├── requirements.txt                   # Python依赖
├── install_setup.sh                   # 安装脚本
└── README.md                          # 说明文档
```

### 扩展功能

可以通过修改代码添加以下功能：
- 频率扫描模式
- 音频文件播放
- 多设备同时控制
- 定时播放任务
- 频谱分析

## 注意事项

1. **音量安全**: 高频率音频可能对听力造成损害，请适当控制音量
2. **设备兼容**: 确保蓝牙音频设备支持所需的频率范围
3. **系统资源**: 高频率播放可能消耗较多CPU资源
4. **法规遵守**: 某些频率可能受到当地法规限制

## 许可证

本项目仅供学习和研究使用。

## 支持

如有问题，请检查：
1. 系统日志: `journalctl -u bluetooth-audio-controller`
2. 蓝牙状态: `bluetoothctl info`
3. 音频设备: `pactl list sinks`
