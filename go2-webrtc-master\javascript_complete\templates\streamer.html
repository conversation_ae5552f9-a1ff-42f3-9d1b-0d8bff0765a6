<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频推流端 - Video Relay Server</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .video-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .video-container {
            flex: 1;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .controls button {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .settings {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .settings label {
            display: inline-block;
            margin: 0 15px;
            font-weight: bold;
        }
        .settings input {
            margin-left: 5px;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 3px;
            text-align: center;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.streaming {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 视频推流端</h1>
        
        <div class="video-section">
            <div class="video-container">
                <h3>本地摄像头</h3>
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
        </div>
        
        <div class="controls">
            <button id="startCameraBtn" class="btn-primary">启动摄像头</button>
            <button id="stopCameraBtn" class="btn-secondary" disabled>停止摄像头</button>
            <button id="connectBtn" class="btn-success" disabled>连接服务器</button>
            <button id="disconnectBtn" class="btn-danger" disabled>断开连接</button>
            <button id="startStreamBtn" class="btn-primary" disabled>开始推流</button>
            <button id="stopStreamBtn" class="btn-secondary" disabled>停止推流</button>
        </div>
        
        <div class="settings">
            <label>房间ID: <input type="text" id="roomId" value="test_room"></label>
            <label>质量: <input type="range" id="quality" min="0.1" max="1" step="0.1" value="0.8"></label>
            <label>帧率: <input type="range" id="frameRate" min="5" max="60" step="5" value="25"></label>
            <label>最大宽度: <input type="number" id="maxWidth" value="1280"></label>
            <label>最大高度: <input type="number" id="maxHeight" value="720"></label>
        </div>
        
        <div class="stats">
            <h3>状态信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div>连接状态</div>
                    <div id="connectionStatus" class="status disconnected">未连接</div>
                </div>
                <div class="stat-item">
                    <div>推流状态</div>
                    <div id="streamingStatus" class="status disconnected">未推流</div>
                </div>
                <div class="stat-item">
                    <div>已发送帧数</div>
                    <div id="framesSent">0</div>
                </div>
                <div class="stat-item">
                    <div>平均帧大小</div>
                    <div id="avgFrameSize">0 KB</div>
                </div>
                <div class="stat-item">
                    <div>当前帧率</div>
                    <div id="currentFps">0 FPS</div>
                </div>
                <div class="stat-item">
                    <div>观看者数量</div>
                    <div id="viewerCount">0</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3>操作日志</h3>
            <div id="log" class="log"></div>
        </div>
    </div>
    
    <script>
        class StreamerApp {
            constructor() {
                this.socket = null;
                this.localVideo = document.getElementById('localVideo');
                this.canvas = document.createElement('canvas');
                this.ctx = this.canvas.getContext('2d');
                this.isStreaming = false;
                this.isConnected = false;
                this.animationId = null;
                
                this.stats = {
                    framesSent: 0,
                    avgFrameSize: 0,
                    startTime: null,
                    lastFrameTime: 0
                };
                
                this.initEventListeners();
                this.updateUI();
            }
            
            initEventListeners() {
                document.getElementById('startCameraBtn').onclick = () => this.startCamera();
                document.getElementById('stopCameraBtn').onclick = () => this.stopCamera();
                document.getElementById('connectBtn').onclick = () => this.connect();
                document.getElementById('disconnectBtn').onclick = () => this.disconnect();
                document.getElementById('startStreamBtn').onclick = () => this.startStreaming();
                document.getElementById('stopStreamBtn').onclick = () => this.stopStreaming();
            }
            
            async startCamera() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: { width: 1280, height: 720 },
                        audio: false
                    });
                    this.localVideo.srcObject = stream;
                    this.log('摄像头启动成功');
                    this.updateUI();
                } catch (error) {
                    this.log('摄像头启动失败: ' + error.message);
                }
            }
            
            stopCamera() {
                if (this.localVideo.srcObject) {
                    this.localVideo.srcObject.getTracks().forEach(track => track.stop());
                    this.localVideo.srcObject = null;
                    this.log('摄像头已停止');
                    this.updateUI();
                }
            }
            
            connect() {
                this.socket = io();
                
                this.socket.on('connect', () => {
                    this.isConnected = true;
                    this.log('已连接到服务器');
                    this.updateUI();
                });
                
                this.socket.on('disconnect', () => {
                    this.isConnected = false;
                    this.log('与服务器断开连接');
                    this.updateUI();
                });
                
                this.socket.on('streamer_joined', (data) => {
                    this.log(`成功加入房间: ${data.room_id}`);
                });
                
                this.socket.on('error', (data) => {
                    this.log('服务器错误: ' + data.message);
                });
            }
            
            disconnect() {
                if (this.socket) {
                    this.socket.disconnect();
                    this.socket = null;
                    this.isConnected = false;
                    this.log('已断开连接');
                    this.updateUI();
                }
            }
            
            startStreaming() {
                if (!this.isConnected || !this.localVideo.srcObject) {
                    this.log('请先启动摄像头并连接服务器');
                    return;
                }
                
                const roomId = document.getElementById('roomId').value;
                this.socket.emit('join_as_streamer', { room_id: roomId });
                
                this.isStreaming = true;
                this.stats.startTime = Date.now();
                this.stats.framesSent = 0;
                this.log('开始推流...');
                this.captureFrames();
                this.updateUI();
            }
            
            stopStreaming() {
                this.isStreaming = false;
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }
                this.log('推流已停止');
                this.updateUI();
            }
            
            captureFrames() {
                if (!this.isStreaming) return;
                
                const now = Date.now();
                const frameRate = parseInt(document.getElementById('frameRate').value);
                const frameInterval = 1000 / frameRate;
                
                if (now - this.stats.lastFrameTime >= frameInterval) {
                    this.captureFrame();
                    this.stats.lastFrameTime = now;
                }
                
                this.animationId = requestAnimationFrame(() => this.captureFrames());
            }
            
            captureFrame() {
                if (this.localVideo.readyState < 2) return;
                
                const quality = parseFloat(document.getElementById('quality').value);
                const maxWidth = parseInt(document.getElementById('maxWidth').value);
                const maxHeight = parseInt(document.getElementById('maxHeight').value);
                
                const videoWidth = this.localVideo.videoWidth;
                const videoHeight = this.localVideo.videoHeight;
                
                if (videoWidth === 0 || videoHeight === 0) return;
                
                const scale = Math.min(maxWidth / videoWidth, maxHeight / videoHeight, 1);
                const scaledWidth = Math.floor(videoWidth * scale);
                const scaledHeight = Math.floor(videoHeight * scale);
                
                this.canvas.width = scaledWidth;
                this.canvas.height = scaledHeight;
                
                this.ctx.drawImage(this.localVideo, 0, 0, scaledWidth, scaledHeight);
                
                this.canvas.toBlob((blob) => {
                    if (blob && this.socket && this.isStreaming) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const base64Data = reader.result.split(',')[1];
                            const roomId = document.getElementById('roomId').value;
                            
                            this.socket.emit('video_frame', {
                                room_id: roomId,
                                frame_data: base64Data,
                                timestamp: Date.now(),
                                size: blob.size,
                                width: scaledWidth,
                                height: scaledHeight
                            });
                            
                            this.stats.framesSent++;
                            this.updateStats();
                        };
                        reader.readAsDataURL(blob);
                    }
                }, 'image/jpeg', quality);
            }
            
            updateStats() {
                document.getElementById('framesSent').textContent = this.stats.framesSent;
                
                if (this.stats.startTime) {
                    const elapsed = (Date.now() - this.stats.startTime) / 1000;
                    const fps = this.stats.framesSent / elapsed;
                    document.getElementById('currentFps').textContent = fps.toFixed(1) + ' FPS';
                }
            }
            
            updateUI() {
                const hasCamera = !!this.localVideo.srcObject;
                
                document.getElementById('startCameraBtn').disabled = hasCamera;
                document.getElementById('stopCameraBtn').disabled = !hasCamera;
                document.getElementById('connectBtn').disabled = this.isConnected || !hasCamera;
                document.getElementById('disconnectBtn').disabled = !this.isConnected;
                document.getElementById('startStreamBtn').disabled = !this.isConnected || this.isStreaming;
                document.getElementById('stopStreamBtn').disabled = !this.isStreaming;
                
                const connectionStatus = document.getElementById('connectionStatus');
                connectionStatus.textContent = this.isConnected ? '已连接' : '未连接';
                connectionStatus.className = 'status ' + (this.isConnected ? 'connected' : 'disconnected');
                
                const streamingStatus = document.getElementById('streamingStatus');
                streamingStatus.textContent = this.isStreaming ? '推流中' : '未推流';
                streamingStatus.className = 'status ' + (this.isStreaming ? 'streaming' : 'disconnected');
            }
            
            log(message) {
                const logElement = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                logElement.innerHTML += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        // 初始化应用
        const app = new StreamerApp();
    </script>
</body>
</html>