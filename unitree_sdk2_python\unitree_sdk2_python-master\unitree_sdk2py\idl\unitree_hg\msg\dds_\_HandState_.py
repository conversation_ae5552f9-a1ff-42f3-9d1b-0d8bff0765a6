"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.11.0
  Module: unitree_hg.msg.dds_
  IDL file: HandState_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
# import unitree_hg


@dataclass
@annotate.final
@annotate.autoid("sequential")
class HandState_(idl.IdlStruct, typename="unitree_hg.msg.dds_.HandState_"):
    motor_state: types.sequence['unitree_sdk2py.idl.unitree_hg.msg.dds_.MotorState_']
    press_sensor_state: types.sequence['unitree_sdk2py.idl.unitree_hg.msg.dds_.PressSensorState_']
    imu_state: 'unitree_sdk2py.idl.unitree_hg.msg.dds_.IMUState_'
    power_v: types.float32
    power_a: types.float32
    system_v: types.float32
    device_v: types.float32
    error: types.array[types.uint32, 2]
    reserve: types.array[types.uint32, 2]


