/**
 * 视频中继集成模块
 * 将Canvas视频流处理集成到现有的Go2WebRTC系统中
 */

import { CanvasStreamer, CanvasViewer } from './canvas_streamer.js';

class VideoRelayIntegration {
    constructor(relayServerUrl = 'http://localhost:5000', options = {}) {
        this.relayServerUrl = relayServerUrl;
        this.options = {
            roomId: options.roomId || 'go2_robot_stream',
            quality: options.quality || 0.8,
            frameRate: options.frameRate || 25,
            maxWidth: options.maxWidth || 1280,
            maxHeight: options.maxHeight || 720,
            autoStart: options.autoStart !== false,
            ...options
        };
        
        this.streamer = null;
        this.viewer = null;
        this.isRelayActive = false;
        this.sourceVideoElement = null;
        
        // 状态回调
        this.onRelayStateChange = null;
        this.onStatsUpdate = null;
        this.onError = null;
        
        // 统计信息
        this.stats = {
            relayStartTime: null,
            totalFramesRelayed: 0,
            avgLatency: 0,
            connectionState: 'disconnected'
        };
        
        this.initializeUI();
    }
    
    initializeUI() {
        // 创建中继控制面板
        this.createRelayControlPanel();
    }
    
    createRelayControlPanel() {
        // 检查是否已存在控制面板
        if (document.getElementById('video-relay-panel')) {
            return;
        }
        
        const panel = document.createElement('div');
        panel.id = 'video-relay-panel';
        panel.innerHTML = `
            <div class="relay-panel">
                <h3>视频中继控制</h3>
                <div class="relay-controls">
                    <button id="start-relay-btn" class="btn btn-primary">开始中继</button>
                    <button id="stop-relay-btn" class="btn btn-secondary" disabled>停止中继</button>
                    <button id="toggle-viewer-btn" class="btn btn-info">显示/隐藏观看窗口</button>
                </div>
                <div class="relay-settings">
                    <label>房间ID: <input type="text" id="room-id-input" value="${this.options.roomId}"></label>
                    <label>质量: <input type="range" id="quality-slider" min="0.1" max="1" step="0.1" value="${this.options.quality}"></label>
                    <label>帧率: <input type="range" id="framerate-slider" min="5" max="60" step="5" value="${this.options.frameRate}"></label>
                </div>
                <div class="relay-stats">
                    <div class="stats-row">
                        <span>状态: <span id="relay-status">未连接</span></span>
                        <span>已中继帧数: <span id="frames-relayed">0</span></span>
                    </div>
                    <div class="stats-row">
                        <span>平均延迟: <span id="avg-latency">0ms</span></span>
                        <span>连接时长: <span id="connection-duration">0s</span></span>
                    </div>
                </div>
                <div id="viewer-window" class="viewer-window" style="display: none;">
                    <h4>中继预览</h4>
                    <canvas id="relay-preview-canvas" width="640" height="480"></canvas>
                </div>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .relay-panel {
                position: fixed;
                top: 10px;
                right: 10px;
                width: 300px;
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-family: Arial, sans-serif;
                font-size: 12px;
                z-index: 10000;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }
            .relay-controls {
                margin: 10px 0;
            }
            .relay-controls button {
                margin: 2px;
                padding: 5px 10px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
            }
            .btn-primary { background: #007bff; color: white; }
            .btn-secondary { background: #6c757d; color: white; }
            .btn-info { background: #17a2b8; color: white; }
            .btn:disabled { opacity: 0.5; cursor: not-allowed; }
            .relay-settings {
                margin: 10px 0;
            }
            .relay-settings label {
                display: block;
                margin: 5px 0;
            }
            .relay-settings input {
                width: 100%;
                margin-top: 2px;
            }
            .relay-stats {
                margin: 10px 0;
                font-size: 11px;
            }
            .stats-row {
                display: flex;
                justify-content: space-between;
                margin: 3px 0;
            }
            .viewer-window {
                margin-top: 10px;
                text-align: center;
            }
            #relay-preview-canvas {
                max-width: 100%;
                border: 1px solid #ccc;
                border-radius: 4px;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(panel);
        
        this.bindControlEvents();
    }
    
    bindControlEvents() {
        // 开始中继按钮
        document.getElementById('start-relay-btn').addEventListener('click', () => {
            this.startRelay();
        });
        
        // 停止中继按钮
        document.getElementById('stop-relay-btn').addEventListener('click', () => {
            this.stopRelay();
        });
        
        // 切换观看窗口
        document.getElementById('toggle-viewer-btn').addEventListener('click', () => {
            this.toggleViewerWindow();
        });
        
        // 房间ID变更
        document.getElementById('room-id-input').addEventListener('change', (e) => {
            this.options.roomId = e.target.value;
        });
        
        // 质量调整
        document.getElementById('quality-slider').addEventListener('input', (e) => {
            this.options.quality = parseFloat(e.target.value);
            if (this.streamer) {
                this.streamer.adjustQuality(this.options.quality);
            }
        });
        
        // 帧率调整
        document.getElementById('framerate-slider').addEventListener('input', (e) => {
            this.options.frameRate = parseInt(e.target.value);
            if (this.streamer) {
                this.streamer.adjustFrameRate(this.options.frameRate);
            }
        });
    }
    
    // 集成到现有的WebRTC系统
    integrateWithGo2WebRTC(go2webrtc) {
        // 监听视频轨道事件
        const originalOnVideoTrack = go2webrtc.onVideoTrack;
        go2webrtc.onVideoTrack = (stream) => {
            // 调用原始回调
            if (originalOnVideoTrack) {
                originalOnVideoTrack(stream);
            }
            
            // 获取视频元素
            this.sourceVideoElement = document.getElementById('video-frame');
            
            if (this.options.autoStart && this.sourceVideoElement) {
                // 延迟启动中继，确保视频已开始播放
                setTimeout(() => {
                    if (this.sourceVideoElement.readyState >= 2) {
                        this.startRelay();
                    }
                }, 1000);
            }
        };
        
        // 监听连接状态变化
        const originalOnConnectionStateChange = go2webrtc.onConnectionStateChange;
        go2webrtc.onConnectionStateChange = (state) => {
            if (originalOnConnectionStateChange) {
                originalOnConnectionStateChange(state);
            }
            
            if (state === 'disconnected' && this.isRelayActive) {
                this.stopRelay();
            }
        };
    }
    
    async startRelay() {
        try {
            if (this.isRelayActive) {
                console.log('中继已在运行中');
                return;
            }
            
            if (!this.sourceVideoElement) {
                this.sourceVideoElement = document.getElementById('video-frame');
                if (!this.sourceVideoElement) {
                    throw new Error('未找到源视频元素');
                }
            }
            
            // 创建推流器
            this.streamer = new CanvasStreamer(this.relayServerUrl, {
                roomId: this.options.roomId,
                quality: this.options.quality,
                frameRate: this.options.frameRate,
                maxWidth: this.options.maxWidth,
                maxHeight: this.options.maxHeight
            });
            
            // 设置回调
            this.streamer.onConnectionStateChange = (state) => {
                this.updateRelayStatus(state);
            };
            
            this.streamer.onError = (error) => {
                console.error('中继错误:', error);
                if (this.onError) this.onError(error);
                this.stopRelay();
            };
            
            // 连接并开始推流
            await this.streamer.connect();
            this.streamer.startStreaming(this.sourceVideoElement);
            
            // 启用自适应质量
            this.streamer.enableAdaptiveQuality();
            
            this.isRelayActive = true;
            this.stats.relayStartTime = Date.now();
            
            // 更新UI
            this.updateControlButtons(true);
            this.startStatsUpdate();
            
            console.log('视频中继已启动');
            
        } catch (error) {
            console.error('启动中继失败:', error);
            if (this.onError) this.onError(error);
        }
    }
    
    stopRelay() {
        if (!this.isRelayActive) {
            return;
        }
        
        if (this.streamer) {
            this.streamer.disconnect();
            this.streamer = null;
        }
        
        if (this.viewer) {
            this.viewer.disconnect();
            this.viewer = null;
        }
        
        this.isRelayActive = false;
        this.stats.relayStartTime = null;
        
        // 更新UI
        this.updateControlButtons(false);
        this.updateRelayStatus('disconnected');
        
        console.log('视频中继已停止');
    }
    
    async toggleViewerWindow() {
        const viewerWindow = document.getElementById('viewer-window');
        const isVisible = viewerWindow.style.display !== 'none';
        
        if (isVisible) {
            // 隐藏观看窗口
            viewerWindow.style.display = 'none';
            if (this.viewer) {
                this.viewer.disconnect();
                this.viewer = null;
            }
        } else {
            // 显示观看窗口
            viewerWindow.style.display = 'block';
            
            if (!this.viewer) {
                try {
                    this.viewer = new CanvasViewer(this.relayServerUrl, {
                        roomId: this.options.roomId
                    });
                    
                    await this.viewer.connect();
                    
                    const canvas = document.getElementById('relay-preview-canvas');
                    this.viewer.setTargetElement(canvas);
                    
                    console.log('观看窗口已启动');
                } catch (error) {
                    console.error('启动观看窗口失败:', error);
                    viewerWindow.style.display = 'none';
                }
            }
        }
    }
    
    updateControlButtons(relayActive) {
        const startBtn = document.getElementById('start-relay-btn');
        const stopBtn = document.getElementById('stop-relay-btn');
        
        startBtn.disabled = relayActive;
        stopBtn.disabled = !relayActive;
    }
    
    updateRelayStatus(status) {
        const statusElement = document.getElementById('relay-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.style.color = status === 'connected' ? '#28a745' : 
                                       status === 'error' ? '#dc3545' : '#ffc107';
        }
        
        this.stats.connectionState = status;
        
        if (this.onRelayStateChange) {
            this.onRelayStateChange(status);
        }
    }
    
    startStatsUpdate() {
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
        }
        
        this.statsInterval = setInterval(() => {
            this.updateStatsDisplay();
        }, 1000);
    }
    
    updateStatsDisplay() {
        if (!this.isRelayActive) {
            if (this.statsInterval) {
                clearInterval(this.statsInterval);
                this.statsInterval = null;
            }
            return;
        }
        
        // 更新帧数
        const framesElement = document.getElementById('frames-relayed');
        if (framesElement && this.streamer) {
            const stats = this.streamer.getStats();
            framesElement.textContent = stats.framesSent;
        }
        
        // 更新延迟
        const latencyElement = document.getElementById('avg-latency');
        if (latencyElement && this.viewer) {
            const stats = this.viewer.getStats();
            latencyElement.textContent = `${Math.round(stats.avgLatency)}ms`;
        }
        
        // 更新连接时长
        const durationElement = document.getElementById('connection-duration');
        if (durationElement && this.stats.relayStartTime) {
            const duration = Math.floor((Date.now() - this.stats.relayStartTime) / 1000);
            durationElement.textContent = `${duration}s`;
        }
        
        // 触发统计回调
        if (this.onStatsUpdate) {
            this.onStatsUpdate(this.getStats());
        }
    }
    
    getStats() {
        const streamerStats = this.streamer ? this.streamer.getStats() : {};
        const viewerStats = this.viewer ? this.viewer.getStats() : {};
        
        return {
            ...this.stats,
            streamer: streamerStats,
            viewer: viewerStats,
            isActive: this.isRelayActive
        };
    }
    
    // 销毁实例
    destroy() {
        this.stopRelay();
        
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
        }
        
        // 移除UI
        const panel = document.getElementById('video-relay-panel');
        if (panel) {
            panel.remove();
        }
    }
}

// 全局实例
let globalVideoRelay = null;

// 便捷函数
function initializeVideoRelay(relayServerUrl, options = {}) {
    if (globalVideoRelay) {
        globalVideoRelay.destroy();
    }
    
    globalVideoRelay = new VideoRelayIntegration(relayServerUrl, options);
    
    // 自动集成到现有的WebRTC系统
    if (window.rtc) {
        globalVideoRelay.integrateWithGo2WebRTC(window.rtc);
    }
    
    return globalVideoRelay;
}

// ES6 模块导出
export { VideoRelayIntegration, initializeVideoRelay };

// 兼容性导出（用于非模块环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { VideoRelayIntegration, initializeVideoRelay };
} else {
    window.VideoRelayIntegration = VideoRelayIntegration;
    window.initializeVideoRelay = initializeVideoRelay;
}