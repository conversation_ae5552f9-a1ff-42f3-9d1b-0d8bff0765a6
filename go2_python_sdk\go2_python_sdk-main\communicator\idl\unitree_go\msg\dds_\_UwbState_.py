"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.10.2
  Module: unitree_go.msg.dds_
  IDL file: UwbState_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
import unitree_go


@dataclass
@annotate.final
@annotate.autoid("sequential")
class UwbState_(idl.IdlStruct, typename="unitree_go.msg.dds_.UwbState_"):
    version: types.array[types.uint8, 2]
    channel: types.uint8
    joy_mode: types.uint8
    orientation_est: types.float32
    pitch_est: types.float32
    distance_est: types.float32
    yaw_est: types.float32
    tag_roll: types.float32
    tag_pitch: types.float32
    tag_yaw: types.float32
    base_roll: types.float32
    base_pitch: types.float32
    base_yaw: types.float32
    joystick: types.array[types.float32, 2]
    error_state: types.uint8
    buttons: types.uint8
    enabled_from_app: types.uint8


