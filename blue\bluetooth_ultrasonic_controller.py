#!/usr/bin/env python3
"""
蓝牙超声波音频控制器
用于在树莓派上控制蓝牙音频设备播放超声波频率
"""

import threading
import time
import json
import subprocess
import re
import wave
import tempfile
import os
import platform
import sys
from flask import Flask, request, jsonify
from flask_cors import CORS
from flasgger import Swagger, swag_from
import logging

# Windows音频支持
if platform.system() == 'Windows':
    try:
        import winsound
        WINDOWS_AUDIO = True
    except ImportError:
        WINDOWS_AUDIO = False
        try:
            import pygame
            PYGAME_AUDIO = True
        except ImportError:
            PYGAME_AUDIO = False
else:
    WINDOWS_AUDIO = False
    PYGAME_AUDIO = False

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BluetoothAudioController:
    def __init__(self):
        self.sample_rate = 192000  # 提高采样率以支持100kHz频率
        self.is_playing = False
        self.current_frequency = 1000  # 默认1kHz
        self.volume = 0.5  # 音量 0.0-1.0
        self.play_thread = None
        self.connected_device = None
        self.temp_audio_file = None
        self.audio_process = None

        # 蓝牙控制将使用系统命令
        self.bluetooth_available = self.check_bluetooth_available()

        # 检查音频播放命令
        self.audio_player = self.find_audio_player()

    def find_audio_player(self):
        """查找可用的音频播放器"""
        if platform.system() == 'Windows':
            if WINDOWS_AUDIO:
                return 'winsound'
            elif PYGAME_AUDIO:
                return 'pygame'
            else:
                # 尝试查找Windows音频播放器
                players = ['powershell', 'ffplay.exe']
                for player in players:
                    try:
                        if player == 'powershell':
                            # 检查PowerShell是否可用
                            result = subprocess.run(['powershell', '-Command', 'Get-Command'],
                                                  capture_output=True, text=True, timeout=5)
                            if result.returncode == 0:
                                return 'powershell'
                        else:
                            result = subprocess.run(['where', player], capture_output=True, text=True)
                            if result.returncode == 0:
                                return player
                    except:
                        continue
                return 'winsound'  # 默认使用winsound
        else:
            # Linux/Unix系统
            players = ['aplay', 'paplay', 'sox', 'ffplay']
            for player in players:
                try:
                    result = subprocess.run(['which', player], capture_output=True, text=True)
                    if result.returncode == 0:
                        return player
                except:
                    continue
            return None

    def generate_wave_file(self, frequency, duration=1.0):
        """生成WAV文件"""
        import math
        import array

        frames = int(duration * self.sample_rate)
        audio_data = array.array('h')  # 'h' = signed short (16-bit)

        for i in range(frames):
            t = float(i) / self.sample_rate
            sample = math.sin(2 * math.pi * frequency * t) * self.volume
            sample_int = int(sample * 32767)
            sample_int = max(-32768, min(32767, sample_int))
            audio_data.append(sample_int)

        # 创建临时WAV文件
        if self.temp_audio_file:
            try:
                os.unlink(self.temp_audio_file)
            except:
                pass

        fd, self.temp_audio_file = tempfile.mkstemp(suffix='.wav')
        os.close(fd)

        # 写入WAV文件
        with wave.open(self.temp_audio_file, 'wb') as wav_file:
            wav_file.setnchannels(1)  # 单声道
            wav_file.setsampwidth(2)  # 16位
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(audio_data.tobytes())

        return self.temp_audio_file

    def play_frequency(self, frequency):
        """播放指定频率"""
        if not self.audio_player:
            logger.error("没有找到可用的音频播放器")
            return False

        self.current_frequency = frequency

        def audio_loop():
            while self.is_playing:
                try:
                    if platform.system() == 'Windows':
                        self.play_windows_audio(self.current_frequency)
                    else:
                        self.play_linux_audio(self.current_frequency)

                except Exception as e:
                    logger.error(f"音频播放错误: {e}")
                    break

        self.is_playing = True
        self.play_thread = threading.Thread(target=audio_loop)
        self.play_thread.daemon = True
        self.play_thread.start()
        return True

    def play_windows_audio(self, frequency):
        """Windows音频播放"""
        if self.audio_player == 'winsound':
            # 使用winsound播放
            wav_file = self.generate_wave_file(frequency, 1.0)
            if WINDOWS_AUDIO:
                import winsound
                winsound.PlaySound(wav_file, winsound.SND_FILENAME)
            else:
                logger.error("winsound不可用")

        elif self.audio_player == 'pygame':
            # 使用pygame播放
            if PYGAME_AUDIO:
                import pygame
                if not pygame.mixer.get_init():
                    pygame.mixer.init(frequency=self.sample_rate, size=-16, channels=1, buffer=512)
                wav_file = self.generate_wave_file(frequency, 1.0)
                sound = pygame.mixer.Sound(wav_file)
                sound.play()
                time.sleep(1.0)  # 播放1秒
            else:
                logger.error("pygame不可用")

        elif self.audio_player == 'powershell':
            # 使用PowerShell播放
            wav_file = self.generate_wave_file(frequency, 1.0)
            ps_script = f'''
            Add-Type -AssemblyName presentationCore
            $mediaPlayer = New-Object system.windows.media.mediaplayer
            $mediaPlayer.open([uri]"{wav_file}")
            $mediaPlayer.Play()
            Start-Sleep -Seconds 1
            $mediaPlayer.Stop()
            '''
            self.audio_process = subprocess.Popen(['powershell', '-Command', ps_script],
                                                stdout=subprocess.DEVNULL,
                                                stderr=subprocess.DEVNULL)
            self.audio_process.wait()
        else:
            # 默认使用winsound
            wav_file = self.generate_wave_file(frequency, 1.0)
            if WINDOWS_AUDIO:
                import winsound
                winsound.PlaySound(wav_file, winsound.SND_FILENAME)

    def play_linux_audio(self, frequency):
        """Linux音频播放"""
        # 生成WAV文件
        wav_file = self.generate_wave_file(frequency, 1.0)

        # 使用系统命令播放
        if self.audio_player == 'aplay':
            cmd = ['aplay', '-q', wav_file]
        elif self.audio_player == 'paplay':
            cmd = ['paplay', wav_file]
        elif self.audio_player == 'sox':
            cmd = ['play', '-q', wav_file]
        elif self.audio_player == 'ffplay':
            cmd = ['ffplay', '-nodisp', '-autoexit', '-loglevel', 'quiet', wav_file]
        else:
            cmd = ['aplay', '-q', wav_file]

        # 启动播放进程
        self.audio_process = subprocess.Popen(cmd,
                                            stdout=subprocess.DEVNULL,
                                            stderr=subprocess.DEVNULL)
        self.audio_process.wait()

    def pause(self):
        """暂停播放"""
        self.is_playing = False
        if self.audio_process:
            try:
                self.audio_process.terminate()
                self.audio_process.wait(timeout=2)
            except:
                try:
                    self.audio_process.kill()
                except:
                    pass
        if self.play_thread:
            self.play_thread.join(timeout=1.0)

    def resume(self):
        """恢复播放"""
        return self.play_frequency(self.current_frequency)

    def set_volume(self, volume):
        """设置音量 (0.0-1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        return True

    def set_frequency(self, frequency):
        """设置频率"""
        self.current_frequency = max(100, min(100000, frequency))  # 限制在100Hz-100kHz
        return True

    def check_bluetooth_available(self):
        """检查蓝牙是否可用"""
        try:
            result = subprocess.run(['bluetoothctl', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False

    def get_bluetooth_devices(self):
        """获取可用的蓝牙设备"""
        if not self.bluetooth_available:
            return []

        try:
            # 使用bluetoothctl命令获取设备列表
            result = subprocess.run(['bluetoothctl', 'devices'],
                                  capture_output=True, text=True, timeout=10)

            devices = []
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        # 解析格式: "Device AA:BB:CC:DD:EE:FF Device Name"
                        match = re.match(r'Device\s+([A-F0-9:]{17})\s+(.+)', line)
                        if match:
                            address = match.group(1)
                            name = match.group(2)

                            # 检查连接状态
                            connected = self.check_device_connected(address)

                            devices.append({
                                'name': name,
                                'address': address,
                                'connected': connected
                            })

            return devices
        except Exception as e:
            logger.error(f"获取蓝牙设备失败: {e}")
            return []

    def check_device_connected(self, address):
        """检查设备是否已连接"""
        try:
            result = subprocess.run(['bluetoothctl', 'info', address],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return 'Connected: yes' in result.stdout
            return False
        except Exception:
            return False

    def connect_bluetooth_device(self, device_address):
        """连接蓝牙设备"""
        try:
            # 使用bluetoothctl命令连接设备
            result = subprocess.run(
                ['bluetoothctl', 'connect', device_address],
                capture_output=True, text=True, timeout=30
            )
            if result.returncode == 0:
                self.connected_device = device_address
                return True
            else:
                logger.error(f"蓝牙连接失败: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"蓝牙连接异常: {e}")
            return False

    def disconnect_bluetooth_device(self, device_address):
        """断开蓝牙设备"""
        try:
            result = subprocess.run(
                ['bluetoothctl', 'disconnect', device_address],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                self.connected_device = None
                return True
            else:
                logger.error(f"蓝牙断开失败: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"蓝牙断开异常: {e}")
            return False

    def get_status(self):
        """获取当前状态"""
        return {
            'is_playing': self.is_playing,
            'frequency': self.current_frequency,
            'volume': self.volume,
            'connected_device': self.connected_device,
            'sample_rate': self.sample_rate
        }

    def cleanup(self):
        """清理资源"""
        self.pause()
        if self.temp_audio_file:
            try:
                os.unlink(self.temp_audio_file)
            except:
                pass

# 创建控制器实例
controller = BluetoothAudioController()

# Flask API服务器
app = Flask(__name__)
CORS(app)

# Swagger配置
swagger_config = {
    "headers": [],
    "specs": [
        {
            "endpoint": 'apispec',
            "route": '/apispec.json',
            "rule_filter": lambda rule: True,
            "model_filter": lambda tag: True,
        }
    ],
    "static_url_path": "/flasgger_static",
    "swagger_ui": True,
    "specs_route": "/docs/"
}

swagger_template = {
    "swagger": "2.0",
    "info": {
        "title": "蓝牙超声波音频控制器 API",
        "description": "用于控制蓝牙音频设备播放超声波频率的API接口",
        "version": "1.0.0",
        "contact": {
            "name": "蓝牙音频控制器",
            "url": "http://localhost:5000"
        }
    },
    "host": "localhost:5000",
    "basePath": "/api",
    "schemes": ["http"],
    "consumes": ["application/json"],
    "produces": ["application/json"]
}

swagger = Swagger(app, config=swagger_config, template=swagger_template)

@app.route('/')
def index():
    """
    主页 - API文档入口
    ---
    tags:
      - 首页
    responses:
      200:
        description: 返回API信息和文档链接
        schema:
          type: object
          properties:
            name:
              type: string
              example: 蓝牙超声波音频控制器
            version:
              type: string
              example: 1.0.0
            docs:
              type: string
              example: http://localhost:5000/docs/
            api_base:
              type: string
              example: http://localhost:5000/api
    """
    return jsonify({
        'name': '蓝牙超声波音频控制器',
        'version': '1.0.0',
        'description': '用于控制蓝牙音频设备播放超声波频率',
        'docs': request.host_url + 'docs/',
        'api_base': request.host_url + 'api',
        'endpoints': {
            'play': 'POST /api/play',
            'pause': 'POST /api/pause',
            'resume': 'POST /api/resume',
            'volume': 'POST /api/volume',
            'frequency': 'POST /api/frequency',
            'status': 'GET /api/status',
            'bluetooth_devices': 'GET /api/bluetooth/devices',
            'bluetooth_connect': 'POST /api/bluetooth/connect',
            'bluetooth_disconnect': 'POST /api/bluetooth/disconnect'
        }
    })

@app.route('/api/play', methods=['POST'])
def api_play():
    """
    开始播放指定频率
    ---
    tags:
      - 播放控制
    parameters:
      - name: body
        in: body
        required: false
        schema:
          type: object
          properties:
            frequency:
              type: integer
              description: 播放频率 (Hz)
              minimum: 100
              maximum: 100000
              example: 1000
    responses:
      200:
        description: 播放成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: success
            message:
              type: string
              example: 开始播放 1000Hz
      500:
        description: 播放失败
        schema:
          type: object
          properties:
            status:
              type: string
              example: error
            message:
              type: string
              example: 播放失败
    """
    data = request.get_json() or {}
    frequency = data.get('frequency', controller.current_frequency)

    if controller.play_frequency(frequency):
        return jsonify({'status': 'success', 'message': f'开始播放 {frequency}Hz'})
    else:
        return jsonify({'status': 'error', 'message': '播放失败'}), 500

@app.route('/api/pause', methods=['POST'])
def api_pause():
    """
    暂停播放
    ---
    tags:
      - 播放控制
    responses:
      200:
        description: 暂停成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: success
            message:
              type: string
              example: 已暂停
    """
    controller.pause()
    return jsonify({'status': 'success', 'message': '已暂停'})

@app.route('/api/resume', methods=['POST'])
def api_resume():
    """恢复播放"""
    if controller.resume():
        return jsonify({'status': 'success', 'message': '已恢复播放'})
    else:
        return jsonify({'status': 'error', 'message': '恢复播放失败'}), 500

@app.route('/api/volume', methods=['POST'])
def api_set_volume():
    """
    设置音量
    ---
    tags:
      - 音频设置
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          required:
            - volume
          properties:
            volume:
              type: number
              description: 音量大小 (0.0-1.0)
              minimum: 0.0
              maximum: 1.0
              example: 0.5
    responses:
      200:
        description: 音量设置成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: success
            message:
              type: string
              example: 音量设置为 0.5
      400:
        description: 参数错误
        schema:
          type: object
          properties:
            status:
              type: string
              example: error
            message:
              type: string
              example: 缺少音量参数
      500:
        description: 设置失败
    """
    data = request.get_json()
    if not data or 'volume' not in data:
        return jsonify({'status': 'error', 'message': '缺少音量参数'}), 400

    volume = data['volume']
    if controller.set_volume(volume):
        return jsonify({'status': 'success', 'message': f'音量设置为 {volume}'})
    else:
        return jsonify({'status': 'error', 'message': '音量设置失败'}), 500

@app.route('/api/frequency', methods=['POST'])
def api_set_frequency():
    """设置频率"""
    data = request.get_json()
    if not data or 'frequency' not in data:
        return jsonify({'status': 'error', 'message': '缺少频率参数'}), 400
    
    frequency = data['frequency']
    if controller.set_frequency(frequency):
        return jsonify({'status': 'success', 'message': f'频率设置为 {frequency}Hz'})
    else:
        return jsonify({'status': 'error', 'message': '频率设置失败'}), 500

@app.route('/api/bluetooth/devices', methods=['GET'])
def api_get_bluetooth_devices():
    """获取蓝牙设备列表"""
    devices = controller.get_bluetooth_devices()
    return jsonify({'status': 'success', 'devices': devices})

@app.route('/api/bluetooth/connect', methods=['POST'])
def api_connect_bluetooth():
    """连接蓝牙设备"""
    data = request.get_json()
    if not data or 'address' not in data:
        return jsonify({'status': 'error', 'message': '缺少设备地址'}), 400
    
    address = data['address']
    if controller.connect_bluetooth_device(address):
        return jsonify({'status': 'success', 'message': f'已连接到 {address}'})
    else:
        return jsonify({'status': 'error', 'message': '连接失败'}), 500

@app.route('/api/bluetooth/disconnect', methods=['POST'])
def api_disconnect_bluetooth():
    """断开蓝牙设备"""
    data = request.get_json()
    if not data or 'address' not in data:
        return jsonify({'status': 'error', 'message': '缺少设备地址'}), 400
    
    address = data['address']
    if controller.disconnect_bluetooth_device(address):
        return jsonify({'status': 'success', 'message': f'已断开 {address}'})
    else:
        return jsonify({'status': 'error', 'message': '断开失败'}), 500

@app.route('/api/status', methods=['GET'])
def api_get_status():
    """
    获取当前状态
    ---
    tags:
      - 状态查询
    responses:
      200:
        description: 状态获取成功
        schema:
          type: object
          properties:
            status:
              type: string
              example: success
            data:
              type: object
              properties:
                is_playing:
                  type: boolean
                  description: 是否正在播放
                  example: false
                frequency:
                  type: integer
                  description: 当前频率 (Hz)
                  example: 1000
                volume:
                  type: number
                  description: 当前音量 (0.0-1.0)
                  example: 0.5
                connected_device:
                  type: string
                  description: 已连接的蓝牙设备地址
                  example: null
                sample_rate:
                  type: integer
                  description: 采样率 (Hz)
                  example: 44100
    """
    status = controller.get_status()
    return jsonify({'status': 'success', 'data': status})

if __name__ == '__main__':
    try:
        print("蓝牙超声波音频控制器启动中...")
        print("API服务器运行在: http://0.0.0.0:5000")
        print("按 Ctrl+C 退出")
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n正在关闭...")
    finally:
        controller.cleanup()
