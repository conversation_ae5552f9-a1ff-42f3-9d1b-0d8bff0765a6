"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.11.0
  Module: unitree_go.msg.dds_
  IDL file: SportModeState_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
# import unitree_go


@dataclass
@annotate.final
@annotate.autoid("sequential")
class SportModeState_(idl.IdlStruct, typename="unitree_go.msg.dds_.SportModeState_"):
    stamp: 'unitree_sdk2py.idl.unitree_go.msg.dds_.TimeSpec_'
    error_code: types.uint32
    imu_state: 'unitree_sdk2py.idl.unitree_go.msg.dds_.IMUState_'
    mode: types.uint8
    progress: types.float32
    gait_type: types.uint8
    foot_raise_height: types.float32
    position: types.array[types.float32, 3]
    body_height: types.float32
    velocity: types.array[types.float32, 3]
    yaw_speed: types.float32
    range_obstacle: types.array[types.float32, 4]
    foot_force: types.array[types.int16, 4]
    foot_position_body: types.array[types.float32, 12]
    foot_speed_body: types.array[types.float32, 12]
    path_point: types.array['unitree_sdk2py.idl.unitree_go.msg.dds_.PathPoint_', 10]


