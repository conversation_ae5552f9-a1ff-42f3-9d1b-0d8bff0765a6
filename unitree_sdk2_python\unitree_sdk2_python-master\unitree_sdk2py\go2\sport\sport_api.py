"""
" service name
"""
SPORT_SERVICE_NAME = "sport"


"""
" service api version
"""
SPORT_API_VERSION = "1.0.0.1"


"""
" api id
"""
SPORT_API_ID_DAMP = 1001
SPORT_API_ID_BALANCESTAND = 1002
SPORT_API_ID_STOPMOVE = 1003
SPORT_API_ID_STANDUP = 1004
SPORT_API_ID_STANDDOWN = 1005
SPORT_API_ID_RECOVERYSTAND = 1006
SPORT_API_ID_EULER = 1007
SPORT_API_ID_MOVE = 1008
SPORT_API_ID_SIT = 1009
SPORT_API_ID_RISESIT = 1010
SPORT_API_ID_SWITCHGAIT = 1011
SPORT_API_ID_TRIGGER = 1012
SPORT_API_ID_BODYHEIGHT = 1013
SPORT_API_ID_FOOTRAISEHEIGHT = 1014
SPORT_API_ID_SPEEDLEVEL = 1015
SPORT_API_ID_HELLO = 1016
SPORT_API_ID_STRETCH = 1017
SPORT_API_ID_TRAJECTORYFOLLOW = 1018
SPORT_API_ID_CONTINUOUSGAIT = 1019
SPORT_API_ID_CONTENT = 1020
SPORT_API_ID_WALLOW = 1021
SPORT_API_ID_DANCE1 = 1022
SPORT_API_ID_DANCE2 = 1023
SPORT_API_ID_GETBODYHEIGHT = 1024
SPORT_API_ID_GETFOOTRAISEHEIGHT = 1025
SPORT_API_ID_GETSPEEDLEVEL = 1026
SPORT_API_ID_SWITCHJOYSTICK = 1027
SPORT_API_ID_POSE = 1028
SPORT_API_ID_SCRAPE = 1029
SPORT_API_ID_FRONTFLIP = 1030
SPORT_API_ID_FRONTJUMP = 1031
SPORT_API_ID_FRONTPOUNCE = 1032
SPORT_API_ID_WIGGLEHIPS = 1033
SPORT_API_ID_GETSTATE = 1034
SPORT_API_ID_ECONOMICGAIT = 1035
SPORT_API_ID_HEART = 1036
ROBOT_SPORT_API_ID_DANCE3             = 1037
ROBOT_SPORT_API_ID_DANCE4             = 1038
ROBOT_SPORT_API_ID_HOPSPINLEFT        = 1039
ROBOT_SPORT_API_ID_HOPSPINRIGHT       = 1040

ROBOT_SPORT_API_ID_LEFTFLIP           = 1042
ROBOT_SPORT_API_ID_BACKFLIP           = 1044
ROBOT_SPORT_API_ID_FREEWALK           = 1045
ROBOT_SPORT_API_ID_FREEBOUND          = 1046
ROBOT_SPORT_API_ID_FREEJUMP           = 1047
ROBOT_SPORT_API_ID_FREEAVOID          = 1048
ROBOT_SPORT_API_ID_WALKSTAIR          = 1049
ROBOT_SPORT_API_ID_WALKUPRIGHT        = 1050
ROBOT_SPORT_API_ID_CROSSSTEP          = 1051

"""
" error code
"""
# client side
SPORT_ERR_CLIENT_POINT_PATH = 4101
# server side
SPORT_ERR_SERVER_OVERTIME = 4201
SPORT_ERR_SERVER_NOT_INIT = 4202
