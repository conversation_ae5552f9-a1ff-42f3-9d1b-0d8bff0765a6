"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.10.2
  Module: std_msgs.msg.dds_
  IDL file: String_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
import std_msgs


@dataclass
@annotate.final
@annotate.autoid("sequential")
class String_(idl.IdlStruct, typename="std_msgs.msg.dds_.String_"):
    data: str


