"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.11.0
  Module: sensor_msgs.msg.dds_.PointField_Constants
  IDL file: PointField_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
# import sensor_msgs

INT8_ = 1
UINT8_ = 2
INT16_ = 3
UINT16_ = 4
INT32_ = 5
UINT32_ = 6
FLOAT32_ = 7
FLOAT64_ = 8

