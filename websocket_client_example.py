#!/usr/bin/env python3
"""
机器狗WebSocket视频流客户端示例
为你的同事提供的简单接入示例

使用方法：
1. 确保机器狗服务器正在运行 (python jiqigou2.py)
2. 运行此客户端: python websocket_client_example.py
3. 客户端会连接到WebSocket并显示视频流

依赖安装：
pip install websockets opencv-python numpy pillow

作者：AI助手
"""

import asyncio
import json
import logging
import cv2
import numpy as np
from PIL import Image
import io
import websockets
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoStreamClient:
    """WebSocket视频流客户端"""
    
    def __init__(self, server_url="ws://localhost:8000/camera/websocket"):
        self.server_url = server_url
        self.websocket = None
        self.is_connected = False
        self.frame_count = 0
        self.start_time = time.time()
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            logger.info(f"正在连接到服务器: {self.server_url}")
            self.websocket = await websockets.connect(self.server_url)
            self.is_connected = True
            logger.info("WebSocket连接成功！")
            return True
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            logger.info("WebSocket连接已断开")
    
    async def send_message(self, message_type, data=None):
        """发送消息到服务器"""
        if not self.is_connected:
            logger.warning("未连接到服务器")
            return
            
        message = {"type": message_type}
        if data:
            message.update(data)
            
        try:
            await self.websocket.send(json.dumps(message))
            logger.debug(f"发送消息: {message_type}")
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
    
    async def receive_frames(self):
        """接收并处理视频帧"""
        logger.info("开始接收视频流...")
        
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                except Exception as e:
                    logger.error(f"处理消息错误: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("服务器连接已关闭")
            self.is_connected = False
        except Exception as e:
            logger.error(f"接收消息错误: {e}")
    
    async def handle_message(self, data):
        """处理接收到的消息"""
        message_type = data.get('type')
        
        if message_type == 'connected':
            logger.info(f"服务器确认: {data.get('message')}")
            config = data.get('config', {})
            logger.info(f"服务器配置: FPS={config.get('target_fps')}, 质量={config.get('jpeg_quality')}")
            
        elif message_type == 'frame':
            await self.process_frame(data)
            
        elif message_type == 'stats':
            stats = data.get('data', {})
            logger.info(f"服务器统计: FPS={stats.get('current_fps', 0):.1f}, "
                       f"已处理={stats.get('frames_processed', 0)}, "
                       f"连接数={stats.get('connected_clients', 0)}")
            
        elif message_type == 'ping':
            # 响应心跳
            await self.send_message('pong')
            
    async def process_frame(self, frame_data):
        """处理视频帧"""
        try:
            # 将十六进制字符串转换为字节数组
            hex_string = frame_data['data']
            frame_bytes = bytes.fromhex(hex_string)
            
            # 使用PIL解码JPEG
            image = Image.open(io.BytesIO(frame_bytes))
            
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # 显示图像
            cv2.imshow('机器狗视频流 - WebSocket', cv_image)
            
            # 更新帧计数
            self.frame_count += 1
            
            # 每100帧显示一次统计
            if self.frame_count % 100 == 0:
                elapsed = time.time() - self.start_time
                fps = self.frame_count / elapsed
                logger.info(f"客户端统计: 已接收 {self.frame_count} 帧, 平均FPS: {fps:.1f}")
            
            # 检查退出键
            if cv2.waitKey(1) & 0xFF == ord('q'):
                logger.info("用户请求退出")
                return False
                
        except Exception as e:
            logger.error(f"帧处理错误: {e}")
            
        return True
    
    async def request_stats_periodically(self):
        """定期请求服务器统计信息"""
        while self.is_connected:
            await asyncio.sleep(10)  # 每10秒请求一次
            if self.is_connected:
                await self.send_message('get_stats')

async def main():
    """主函数"""
    # 创建客户端实例
    client = VideoStreamClient()
    
    # 连接到服务器
    if not await client.connect():
        return
    
    try:
        # 创建任务
        receive_task = asyncio.create_task(client.receive_frames())
        stats_task = asyncio.create_task(client.request_stats_periodically())
        
        logger.info("视频流客户端已启动")
        logger.info("按 'q' 键退出程序")
        
        # 等待任务完成
        await asyncio.gather(receive_task, stats_task, return_exceptions=True)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序错误: {e}")
    finally:
        # 清理资源
        await client.disconnect()
        cv2.destroyAllWindows()
        logger.info("程序已退出")

if __name__ == "__main__":
    print("=" * 60)
    print("🐕 机器狗WebSocket视频流客户端")
    print("=" * 60)
    print("功能：连接到机器狗服务器并显示实时视频流")
    print("操作：按 'q' 键退出，Ctrl+C 强制退出")
    print("=" * 60)
    
    # 运行客户端
    asyncio.run(main())
