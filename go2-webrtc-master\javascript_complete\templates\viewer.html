<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频观看端 - Video Relay Server</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .video-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .video-container {
            flex: 1;
            min-width: 300px;
            text-align: center;
        }
        .video-display {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #000;
            min-height: 360px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .controls button {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .settings {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .settings label {
            display: inline-block;
            margin: 0 15px;
            font-weight: bold;
        }
        .settings input {
            margin-left: 5px;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 3px;
            text-align: center;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.receiving {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .no-video {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 360px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            color: #6c757d;
            font-size: 18px;
        }
        .video-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📺 视频观看端</h1>
        
        <div class="video-section">
            <div class="video-container">
                <h3>视频流</h3>
                <div id="videoDisplay" class="no-video">
                    等待视频流...
                </div>
                <div id="videoInfo" class="video-info"></div>
            </div>
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-success">连接服务器</button>
            <button id="disconnectBtn" class="btn-danger" disabled>断开连接</button>
            <button id="joinRoomBtn" class="btn-primary" disabled>加入房间</button>
            <button id="leaveRoomBtn" class="btn-secondary" disabled>离开房间</button>
            <button id="requestStatsBtn" class="btn-primary" disabled>获取统计</button>
        </div>
        
        <div class="settings">
            <label>房间ID: <input type="text" id="roomId" value="test_room"></label>
            <label>自动重连: <input type="checkbox" id="autoReconnect" checked></label>
            <label>显示帧信息: <input type="checkbox" id="showFrameInfo" checked></label>
        </div>
        
        <div class="stats">
            <h3>状态信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div>连接状态</div>
                    <div id="connectionStatus" class="status disconnected">未连接</div>
                </div>
                <div class="stat-item">
                    <div>接收状态</div>
                    <div id="receivingStatus" class="status disconnected">未接收</div>
                </div>
                <div class="stat-item">
                    <div>已接收帧数</div>
                    <div id="framesReceived">0</div>
                </div>
                <div class="stat-item">
                    <div>平均帧大小</div>
                    <div id="avgFrameSize">0 KB</div>
                </div>
                <div class="stat-item">
                    <div>当前帧率</div>
                    <div id="currentFps">0 FPS</div>
                </div>
                <div class="stat-item">
                    <div>延迟</div>
                    <div id="latency">0 ms</div>
                </div>
                <div class="stat-item">
                    <div>房间观看者</div>
                    <div id="roomViewers">0</div>
                </div>
                <div class="stat-item">
                    <div>视频分辨率</div>
                    <div id="videoResolution">-</div>
                </div>
            </div>
        </div>
        
        <div>
            <h3>操作日志</h3>
            <div id="log" class="log"></div>
        </div>
    </div>
    
    <script>
        class ViewerApp {
            constructor() {
                this.socket = null;
                this.isConnected = false;
                this.isInRoom = false;
                this.isReceiving = false;
                
                this.stats = {
                    framesReceived: 0,
                    totalFrameSize: 0,
                    startTime: null,
                    lastFrameTime: 0,
                    frameTimestamps: []
                };
                
                this.initEventListeners();
                this.updateUI();
            }
            
            initEventListeners() {
                document.getElementById('connectBtn').onclick = () => this.connect();
                document.getElementById('disconnectBtn').onclick = () => this.disconnect();
                document.getElementById('joinRoomBtn').onclick = () => this.joinRoom();
                document.getElementById('leaveRoomBtn').onclick = () => this.leaveRoom();
                document.getElementById('requestStatsBtn').onclick = () => this.requestStats();
            }
            
            connect() {
                this.socket = io();
                
                this.socket.on('connect', () => {
                    this.isConnected = true;
                    this.log('已连接到服务器');
                    this.updateUI();
                });
                
                this.socket.on('disconnect', () => {
                    this.isConnected = false;
                    this.isInRoom = false;
                    this.isReceiving = false;
                    this.log('与服务器断开连接');
                    this.updateUI();
                    
                    // 自动重连
                    if (document.getElementById('autoReconnect').checked) {
                        setTimeout(() => {
                            if (!this.isConnected) {
                                this.log('尝试自动重连...');
                                this.connect();
                            }
                        }, 3000);
                    }
                });
                
                this.socket.on('viewer_joined', (data) => {
                    this.isInRoom = true;
                    this.log(`成功加入房间: ${data.room_id}`);
                    this.updateUI();
                });
                
                this.socket.on('video_frame', (data) => {
                    this.handleVideoFrame(data);
                });
                
                this.socket.on('stats_update', (data) => {
                    this.updateServerStats(data.stats);
                });
                
                this.socket.on('error', (data) => {
                    this.log('服务器错误: ' + data.message);
                });
                
                this.socket.on('pong', (data) => {
                    const latency = Date.now() - data.timestamp;
                    document.getElementById('latency').textContent = latency + ' ms';
                });
            }
            
            disconnect() {
                if (this.socket) {
                    this.socket.disconnect();
                    this.socket = null;
                    this.isConnected = false;
                    this.isInRoom = false;
                    this.isReceiving = false;
                    this.log('已断开连接');
                    this.updateUI();
                }
            }
            
            joinRoom() {
                if (!this.isConnected) {
                    this.log('请先连接服务器');
                    return;
                }
                
                const roomId = document.getElementById('roomId').value;
                this.socket.emit('join_as_viewer', { room_id: roomId });
                this.stats.startTime = Date.now();
                this.stats.framesReceived = 0;
                this.stats.totalFrameSize = 0;
                this.stats.frameTimestamps = [];
                this.log(`尝试加入房间: ${roomId}`);
            }
            
            leaveRoom() {
                if (this.socket && this.isInRoom) {
                    const roomId = document.getElementById('roomId').value;
                    this.socket.emit('leave_room', { room_id: roomId });
                    this.isInRoom = false;
                    this.isReceiving = false;
                    this.log(`离开房间: ${roomId}`);
                    this.clearVideoDisplay();
                    this.updateUI();
                }
            }
            
            requestStats() {
                if (this.socket && this.isInRoom) {
                    const roomId = document.getElementById('roomId').value;
                    this.socket.emit('get_stats', { room_id: roomId });
                }
            }
            
            handleVideoFrame(data) {
                if (!this.isInRoom) return;
                
                this.isReceiving = true;
                this.stats.framesReceived++;
                this.stats.totalFrameSize += data.size || 0;
                
                // 计算延迟
                const now = Date.now();
                if (data.timestamp) {
                    const frameLatency = now - data.timestamp;
                    document.getElementById('latency').textContent = frameLatency + ' ms';
                }
                
                // 更新帧率计算
                this.stats.frameTimestamps.push(now);
                if (this.stats.frameTimestamps.length > 30) {
                    this.stats.frameTimestamps.shift();
                }
                
                // 显示视频帧
                this.displayFrame(data);
                
                // 更新统计信息
                this.updateStats();
                this.updateUI();
                
                if (document.getElementById('showFrameInfo').checked) {
                    this.log(`接收帧: ${data.width}x${data.height}, ${(data.size/1024).toFixed(1)}KB`);
                }
            }
            
            displayFrame(data) {
                const videoDisplay = document.getElementById('videoDisplay');
                
                // 创建或更新图片元素
                let img = videoDisplay.querySelector('img');
                if (!img) {
                    img = document.createElement('img');
                    img.style.width = '100%';
                    img.style.height = 'auto';
                    img.style.maxWidth = '100%';
                    videoDisplay.innerHTML = '';
                    videoDisplay.appendChild(img);
                    videoDisplay.classList.remove('no-video');
                }
                
                // 设置图片数据
                img.src = `data:image/jpeg;base64,${data.frame_data}`;
                
                // 更新视频信息
                const videoInfo = document.getElementById('videoInfo');
                videoInfo.textContent = `分辨率: ${data.width}x${data.height} | 大小: ${(data.size/1024).toFixed(1)}KB`;
                
                // 更新分辨率显示
                document.getElementById('videoResolution').textContent = `${data.width}x${data.height}`;
            }
            
            clearVideoDisplay() {
                const videoDisplay = document.getElementById('videoDisplay');
                videoDisplay.innerHTML = '等待视频流...';
                videoDisplay.classList.add('no-video');
                
                const videoInfo = document.getElementById('videoInfo');
                videoInfo.textContent = '';
                
                document.getElementById('videoResolution').textContent = '-';
            }
            
            updateStats() {
                document.getElementById('framesReceived').textContent = this.stats.framesReceived;
                
                if (this.stats.framesReceived > 0) {
                    const avgSize = this.stats.totalFrameSize / this.stats.framesReceived;
                    document.getElementById('avgFrameSize').textContent = (avgSize / 1024).toFixed(1) + ' KB';
                }
                
                // 计算帧率
                if (this.stats.frameTimestamps.length > 1) {
                    const timeSpan = this.stats.frameTimestamps[this.stats.frameTimestamps.length - 1] - 
                                   this.stats.frameTimestamps[0];
                    const fps = (this.stats.frameTimestamps.length - 1) / (timeSpan / 1000);
                    document.getElementById('currentFps').textContent = fps.toFixed(1) + ' FPS';
                }
            }
            
            updateServerStats(stats) {
                if (stats.viewers !== undefined) {
                    document.getElementById('roomViewers').textContent = stats.viewers;
                }
            }
            
            updateUI() {
                document.getElementById('connectBtn').disabled = this.isConnected;
                document.getElementById('disconnectBtn').disabled = !this.isConnected;
                document.getElementById('joinRoomBtn').disabled = !this.isConnected || this.isInRoom;
                document.getElementById('leaveRoomBtn').disabled = !this.isInRoom;
                document.getElementById('requestStatsBtn').disabled = !this.isInRoom;
                
                const connectionStatus = document.getElementById('connectionStatus');
                connectionStatus.textContent = this.isConnected ? '已连接' : '未连接';
                connectionStatus.className = 'status ' + (this.isConnected ? 'connected' : 'disconnected');
                
                const receivingStatus = document.getElementById('receivingStatus');
                receivingStatus.textContent = this.isReceiving ? '接收中' : '未接收';
                receivingStatus.className = 'status ' + (this.isReceiving ? 'receiving' : 'disconnected');
            }
            
            log(message) {
                const logElement = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                logElement.innerHTML += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        // 初始化应用
        const app = new ViewerApp();
        
        // 定期发送ping来测试延迟
        setInterval(() => {
            if (app.socket && app.isConnected) {
                app.socket.emit('ping', { timestamp: Date.now() });
            }
        }, 5000);
    </script>
</body>
</html>