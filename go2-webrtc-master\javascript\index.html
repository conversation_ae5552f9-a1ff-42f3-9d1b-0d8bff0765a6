<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Unitree Go2 WebRTC Playground</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/skeleton/2.0.4/skeleton.min.css"
    />
    <style>
      /* Dark mode styling */
      body {
        font-family: Arial, sans-serif;
        background-color: #1f1f1f;
        color: #fff;
      }
      input[type="text"],
      button {
        background-color: #333;
        color: #fff;
        margin-right: 10px;
      }
      /* Additional styling specific to this page */
      .container {
        display: flex;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .input-group {
        margin-bottom: 10px;
      }
      .input-group label {
        margin-right: 10px;
      }
      .button-group {
        display: flex;
        justify-content: space-between;
      }
      .log {
        border: 1px solid #ccc;
        padding: 10px;
        margin-top: 20px;
        max-height: 200px;
        overflow-y: auto;
        background-color: #333;
        color: #fff;
        display: block;
        width: 95%;
        white-space: pre-wrap; /* Preserve line breaks within the code */
      }
      .log-code {
        overflow-y: auto;
        background-color: #333;
        color: #fff;
        display: block;
        white-space: pre-wrap; /* Preserve line breaks within the code */
      }
      .button-primary {
        background-color: #007bff;
        color: #fff;
        border: none;
        padding: 10px 20px;
        cursor: pointer;
      }
      .button-primary:hover {
        background-color: #0056b3;
      }
      #video-frame {
        max-width: 100%;
        height: auto;
        margin-left: 20px; /* Add some space between the input controls and video frame */
      }
      .corner-div {
        position: fixed;
        width: 200px;
        height: 200px;
        color: #fff; /* Text color */
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        font-size: 20px; /* Example font size */
      }

      .bottom-left {
        bottom: 0;
        left: 0;
      }

      .bottom-right {
        bottom: 0;
        right: 0;
      }
    </style>
    <script src="md5.js"></script>
    <script src="joy.min.js"></script>
  </head>
  <body>
    <div class="container">
      <div>
        <div class="input-group">
          <label for="token">Token:</label>
          <input type="text" id="token" name="token" />
        </div>
        <div class="input-group">
          <label for="robot-ip">Robot IP:</label>
          <input type="text" id="robot-ip" name="robot-ip" />
        </div>
        <div class="input-group">
          <label for="command">Command:</label>
          <select id="command"></select>
        </div>
        <div class="input-group">
          <label for="gamepad">Gamepads (Xbox). Hold LB to control.</label>
          <select id="gamepad"></select>
        </div>
        <div class="button-group">
          <button id="connect-btn" class="button-primary">Connect</button>
          <button id="execute-btn" class="button-primary">Execute</button>
        </div>
      </div>
      <video id="video-frame" width="640" height="480" autoplay></video>
    </div>
    <div
      class="input-group"
      style="display: flex; align-items: center; margin-top: 20px"
    >
      <input
        type="text"
        id="custom-command"
        placeholder="Enter custom message"
        style="flex-grow: 1; margin-right: 10px"
      />
      <button id="execute-custom-btn" class="button-primary">Execute</button>
    </div>
    <div>
      <div id="log" class="log">
        <code class="log-code" id="log-code"></code>
      </div>
    </div>

    <div id="joy-left" class="corner-div bottom-left"></div>
    <div id="joy-right" class="corner-div bottom-right"></div>

    <script type="module" src="index.js"></script>
  </body>
</html>
