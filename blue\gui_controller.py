#!/usr/bin/env python3
"""
蓝牙超声波音频控制器 - GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import threading
import time
import json
import platform

# Windows音频设备检测
if platform.system() == 'Windows':
    try:
        import pygame
        PYGAME_AVAILABLE = True
    except ImportError:
        PYGAME_AVAILABLE = False
else:
    PYGAME_AVAILABLE = False

class BluetoothAudioGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("蓝牙超声波音频控制器")
        self.root.geometry("600x500")
        
        # API服务器地址
        self.api_base = "http://localhost:5000/api"
        
        # 当前状态
        self.is_playing = False
        self.current_frequency = 1000
        self.current_volume = 0.5
        self.connected_device = None
        
        self.setup_ui()
        self.update_status()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="蓝牙超声波音频控制器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 频率控制区域
        freq_frame = ttk.LabelFrame(main_frame, text="频率控制 (100Hz - 100kHz)", padding="10")
        freq_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 频率输入框
        freq_input_frame = ttk.Frame(freq_frame)
        freq_input_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(freq_input_frame, text="频率 (Hz):").grid(row=0, column=0, sticky=tk.W)
        self.freq_var = tk.StringVar(value="1000")
        self.freq_entry = ttk.Entry(freq_input_frame, textvariable=self.freq_var, width=10)
        self.freq_entry.grid(row=0, column=1, padx=(5, 10))
        self.freq_entry.bind('<Return>', lambda e: self.set_frequency())

        ttk.Button(freq_input_frame, text="设置频率", command=self.set_frequency).grid(row=0, column=2)

        # 频率滑块控制 (对数刻度)
        freq_slider_frame = ttk.Frame(freq_frame)
        freq_slider_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(freq_slider_frame, text="频率滑块:").grid(row=0, column=0, sticky=tk.W)

        # 使用对数刻度：log10(100) = 2, log10(100000) = 5
        self.freq_scale_var = tk.DoubleVar(value=3.0)  # log10(1000) = 3
        self.freq_scale = ttk.Scale(freq_slider_frame, from_=2.0, to=5.0,
                                   variable=self.freq_scale_var, orient=tk.HORIZONTAL, length=300)
        self.freq_scale.grid(row=0, column=1, padx=(5, 10))
        # 只绑定释放事件，避免拖动时的频繁更新
        self.freq_scale.bind("<ButtonRelease-1>", self.on_freq_scale_release)
        self.freq_scale.bind("<B1-Motion>", self.on_freq_scale_drag)

        self.freq_display_label = ttk.Label(freq_slider_frame, text="1000 Hz")
        self.freq_display_label.grid(row=0, column=2)

        # 频率刻度标签
        scale_labels_frame = ttk.Frame(freq_frame)
        scale_labels_frame.grid(row=2, column=0, columnspan=3, pady=(5, 10))

        scale_labels = ["100Hz", "1kHz", "10kHz", "100kHz"]
        for i, label in enumerate(scale_labels):
            ttk.Label(scale_labels_frame, text=label, font=("Arial", 8)).grid(row=0, column=i, padx=75)

        # 频率预设按钮
        preset_frame = ttk.Frame(freq_frame)
        preset_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))

        presets = [
            ("100Hz", 100),
            ("1kHz", 1000),
            ("10kHz", 10000),
            ("20kHz", 20000),
            ("50kHz", 50000),
            ("100kHz", 100000)
        ]

        for i, (text, freq) in enumerate(presets):
            ttk.Button(preset_frame, text=text,
                      command=lambda f=freq: self.set_preset_frequency(f)).grid(row=0, column=i, padx=2)
        
        # 音量控制区域
        volume_frame = ttk.LabelFrame(main_frame, text="音量控制", padding="10")
        volume_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(volume_frame, text="音量:").grid(row=0, column=0, sticky=tk.W)
        self.volume_var = tk.DoubleVar(value=0.5)
        self.volume_scale = ttk.Scale(volume_frame, from_=0.0, to=1.0, 
                                     variable=self.volume_var, orient=tk.HORIZONTAL, length=200)
        self.volume_scale.grid(row=0, column=1, padx=(5, 10))
        self.volume_scale.bind("<ButtonRelease-1>", self.on_volume_change)
        self.volume_scale.bind("<B1-Motion>", self.on_volume_drag)
        
        self.volume_label = ttk.Label(volume_frame, text="50%")
        self.volume_label.grid(row=0, column=2)
        
        # 播放控制区域
        control_frame = ttk.LabelFrame(main_frame, text="播放控制", padding="10")
        control_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        self.play_button = ttk.Button(control_frame, text="播放", command=self.toggle_play)
        self.play_button.grid(row=0, column=0, padx=(0, 5))

        ttk.Button(control_frame, text="暂停", command=self.pause).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="恢复", command=self.resume).grid(row=0, column=2, padx=5)

        # 添加本地音频测试按钮
        ttk.Button(control_frame, text="本地测试", command=self.test_local_audio).grid(row=0, column=3, padx=5)

        # 音频设备信息
        if platform.system() == 'Windows':
            device_info_frame = ttk.Frame(control_frame)
            device_info_frame.grid(row=1, column=0, columnspan=4, pady=(10, 0))

            ttk.Label(device_info_frame, text="音频支持:").grid(row=0, column=0, sticky=tk.W)

            audio_support = []
            try:
                import winsound
                audio_support.append("WinSound")
            except ImportError:
                pass

            if PYGAME_AVAILABLE:
                audio_support.append("Pygame")

            support_text = ", ".join(audio_support) if audio_support else "无"
            ttk.Label(device_info_frame, text=support_text).grid(row=0, column=1, padx=(5, 0))
        
        # 蓝牙设备区域
        bluetooth_frame = ttk.LabelFrame(main_frame, text="蓝牙设备", padding="10")
        bluetooth_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 设备列表
        self.device_listbox = tk.Listbox(bluetooth_frame, height=4)
        self.device_listbox.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Button(bluetooth_frame, text="刷新设备", command=self.refresh_devices).grid(row=1, column=0, padx=(0, 5))
        ttk.Button(bluetooth_frame, text="连接", command=self.connect_device).grid(row=1, column=1, padx=5)
        ttk.Button(bluetooth_frame, text="断开", command=self.disconnect_device).grid(row=1, column=2, padx=5)
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="10")
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        self.status_text = tk.Text(status_frame, height=4, width=50)
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
    def api_request(self, endpoint, method='GET', data=None):
        """发送API请求"""
        try:
            url = f"{self.api_base}/{endpoint}"
            if method == 'GET':
                response = requests.get(url, timeout=2)  # 减少超时时间
            elif method == 'POST':
                response = requests.post(url, json=data, timeout=2)

            if response.status_code == 200:
                return response.json()
            else:
                return {'status': 'error', 'message': f'HTTP {response.status_code}'}
        except requests.exceptions.RequestException:
            # 静默处理连接错误，避免界面卡顿
            return {'status': 'error', 'message': 'API连接失败'}
    
    def log_message(self, message):
        """在状态文本框中显示消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")

        # 限制日志行数，避免内存占用过多
        lines = self.status_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:  # 保持最多100行日志
            self.status_text.delete("1.0", "2.0")

        self.status_text.see(tk.END)
        # 移除update_idletasks()调用，减少界面更新频率
    
    def on_freq_scale_drag(self, event):
        """频率滑块拖动事件 - 仅更新显示，不发送API请求"""
        import math
        # 将对数刻度转换为实际频率
        log_freq = self.freq_scale_var.get()
        frequency = int(10 ** log_freq)

        # 更新显示
        if frequency >= 1000:
            if frequency >= 1000000:
                display_text = f"{frequency/1000000:.1f}MHz"
            else:
                display_text = f"{frequency/1000:.1f}kHz"
        else:
            display_text = f"{frequency}Hz"

        self.freq_display_label.config(text=display_text)

        # 更新输入框
        self.freq_var.set(str(frequency))

    def on_freq_scale_release(self, event):
        """频率滑块释放事件 - 发送API请求"""
        import math
        # 将对数刻度转换为实际频率
        log_freq = self.freq_scale_var.get()
        frequency = int(10 ** log_freq)

        # 设置频率
        self.set_frequency_internal(frequency)

    def set_frequency(self):
        """设置频率"""
        try:
            frequency = int(self.freq_var.get())
            if frequency < 100 or frequency > 100000:
                messagebox.showerror("错误", "频率必须在100-100000Hz之间")
                return

            # 更新滑块位置
            import math
            log_freq = math.log10(frequency)
            self.freq_scale_var.set(log_freq)

            # 更新显示
            self.on_freq_scale_drag(None)

            # 设置频率
            self.set_frequency_internal(frequency)

        except ValueError:
            messagebox.showerror("错误", "请输入有效的频率数值")

    def set_frequency_internal(self, frequency):
        """内部频率设置方法"""
        result = self.api_request('frequency', 'POST', {'frequency': frequency})
        if result['status'] == 'success':
            self.current_frequency = frequency
            self.log_message(f"频率设置为 {frequency}Hz")
        else:
            self.log_message(f"频率设置失败: {result['message']}")
    
    def set_preset_frequency(self, frequency):
        """设置预设频率"""
        import math

        # 更新输入框
        self.freq_var.set(str(frequency))

        # 更新滑块位置
        log_freq = math.log10(frequency)
        self.freq_scale_var.set(log_freq)

        # 更新显示
        self.on_freq_scale_drag(None)

        # 设置频率
        self.set_frequency_internal(frequency)
    
    def on_volume_drag(self, event):
        """音量拖动事件 - 仅更新显示"""
        volume = self.volume_var.get()
        self.volume_label.config(text=f"{int(volume*100)}%")

    def on_volume_change(self, event):
        """音量改变事件 - 发送API请求"""
        volume = self.volume_var.get()
        result = self.api_request('volume', 'POST', {'volume': volume})
        if result['status'] == 'success':
            self.current_volume = volume
            self.volume_label.config(text=f"{int(volume*100)}%")
            self.log_message(f"音量设置为 {int(volume*100)}%")
        else:
            self.log_message(f"音量设置失败: {result['message']}")
    
    def toggle_play(self):
        """切换播放状态"""
        if self.is_playing:
            self.pause()
        else:
            self.play()
    
    def play(self):
        """开始播放"""
        frequency = int(self.freq_var.get()) if self.freq_var.get().isdigit() else self.current_frequency
        result = self.api_request('play', 'POST', {'frequency': frequency})
        if result['status'] == 'success':
            self.is_playing = True
            self.play_button.config(text="停止")
            self.log_message(f"开始播放 {frequency}Hz")
        else:
            self.log_message(f"播放失败: {result['message']}")
    
    def pause(self):
        """暂停播放"""
        result = self.api_request('pause', 'POST')
        if result['status'] == 'success':
            self.is_playing = False
            self.play_button.config(text="播放")
            self.log_message("已暂停播放")
        else:
            self.log_message(f"暂停失败: {result['message']}")
    
    def resume(self):
        """恢复播放"""
        result = self.api_request('resume', 'POST')
        if result['status'] == 'success':
            self.is_playing = True
            self.play_button.config(text="停止")
            self.log_message("已恢复播放")
        else:
            self.log_message(f"恢复失败: {result['message']}")
    
    def refresh_devices(self):
        """刷新蓝牙设备列表"""
        def refresh_thread():
            result = self.api_request('bluetooth/devices')
            if result['status'] == 'success':
                # 在主线程中更新UI
                self.root.after(0, lambda: self.update_device_list(result['devices']))
            else:
                self.root.after(0, lambda: self.log_message(f"刷新设备失败: {result['message']}"))

        # 在后台线程中执行，避免阻塞UI
        threading.Thread(target=refresh_thread, daemon=True).start()
        self.log_message("正在刷新蓝牙设备...")

    def update_device_list(self, devices):
        """更新设备列表UI"""
        self.device_listbox.delete(0, tk.END)
        for device in devices:
            status = "已连接" if device['connected'] else "未连接"
            display_text = f"{device['name']} ({device['address']}) - {status}"
            self.device_listbox.insert(tk.END, display_text)
        self.log_message(f"发现 {len(devices)} 个蓝牙设备")
    
    def connect_device(self):
        """连接选中的蓝牙设备"""
        selection = self.device_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个设备")
            return

        # 从显示文本中提取MAC地址
        device_text = self.device_listbox.get(selection[0])
        try:
            address = device_text.split('(')[1].split(')')[0]

            def connect_thread():
                self.log_message(f"正在连接到 {address}...")
                result = self.api_request('bluetooth/connect', 'POST', {'address': address})
                if result['status'] == 'success':
                    self.connected_device = address
                    self.root.after(0, lambda: self.log_message(f"已连接到 {address}"))
                    self.root.after(0, self.refresh_devices)  # 刷新状态
                else:
                    self.root.after(0, lambda: self.log_message(f"连接失败: {result['message']}"))

            # 在后台线程中执行连接
            threading.Thread(target=connect_thread, daemon=True).start()

        except IndexError:
            messagebox.showerror("错误", "无法解析设备地址")
    
    def disconnect_device(self):
        """断开选中的蓝牙设备"""
        selection = self.device_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个设备")
            return

        # 从显示文本中提取MAC地址
        device_text = self.device_listbox.get(selection[0])
        try:
            address = device_text.split('(')[1].split(')')[0]

            def disconnect_thread():
                self.log_message(f"正在断开 {address}...")
                result = self.api_request('bluetooth/disconnect', 'POST', {'address': address})
                if result['status'] == 'success':
                    self.connected_device = None
                    self.root.after(0, lambda: self.log_message(f"已断开 {address}"))
                    self.root.after(0, self.refresh_devices)  # 刷新状态
                else:
                    self.root.after(0, lambda: self.log_message(f"断开失败: {result['message']}"))

            # 在后台线程中执行断开
            threading.Thread(target=disconnect_thread, daemon=True).start()

        except IndexError:
            messagebox.showerror("错误", "无法解析设备地址")
    
    def update_status(self):
        """定期更新状态"""
        def status_thread():
            while True:
                try:
                    result = self.api_request('status')
                    if result['status'] == 'success':
                        data = result['data']
                        self.is_playing = data['is_playing']
                        self.current_frequency = data['frequency']
                        self.current_volume = data['volume']
                        self.connected_device = data['connected_device']

                        # 更新UI
                        self.root.after(0, self.update_ui_status)
                except:
                    pass
                time.sleep(5)  # 减少更新频率到每5秒一次

        thread = threading.Thread(target=status_thread, daemon=True)
        thread.start()
    
    def test_local_audio(self):
        """本地音频测试"""
        try:
            frequency = int(self.freq_var.get()) if self.freq_var.get().isdigit() else 1000

            # 验证频率范围
            if frequency < 100 or frequency > 100000:
                self.log_message(f"频率超出范围: {frequency}Hz (支持100Hz-100kHz)")
                return

            volume = self.volume_var.get()

            # 显示频率信息
            if frequency >= 1000:
                if frequency >= 1000000:
                    freq_display = f"{frequency/1000000:.1f}MHz"
                else:
                    freq_display = f"{frequency/1000:.1f}kHz"
            else:
                freq_display = f"{frequency}Hz"

            self.log_message(f"开始本地音频测试: {freq_display}, 音量{int(volume*100)}%")

            if platform.system() == 'Windows':
                self.test_windows_audio(frequency, volume)
            else:
                self.log_message("本地测试仅支持Windows系统")

        except Exception as e:
            self.log_message(f"本地音频测试失败: {str(e)}")

    def test_windows_audio(self, frequency, volume):
        """Windows音频测试"""
        import math
        import array
        import wave
        import tempfile
        import os

        def generate_test_wave():
            # 生成测试音频
            sample_rate = 192000  # 提高采样率以支持高频
            duration = 2.0  # 2秒
            frames = int(duration * sample_rate)
            audio_data = array.array('h')

            for i in range(frames):
                t = float(i) / sample_rate
                sample = math.sin(2 * math.pi * frequency * t) * volume
                sample_int = int(sample * 32767)
                sample_int = max(-32768, min(32767, sample_int))
                audio_data.append(sample_int)

            # 创建临时WAV文件
            fd, temp_file = tempfile.mkstemp(suffix='.wav')
            os.close(fd)

            with wave.open(temp_file, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())

            return temp_file

        def play_test_audio():
            try:
                wav_file = generate_test_wave()

                # 尝试不同的播放方法
                played = False

                # 方法1: 使用winsound
                try:
                    import winsound
                    winsound.PlaySound(wav_file, winsound.SND_FILENAME)
                    played = True
                    self.log_message("✓ 使用WinSound播放成功")
                except Exception as e:
                    self.log_message(f"WinSound播放失败: {str(e)}")

                # 方法2: 使用pygame (如果winsound失败)
                if not played and PYGAME_AVAILABLE:
                    try:
                        import pygame
                        pygame.mixer.init()
                        sound = pygame.mixer.Sound(wav_file)
                        sound.play()
                        import time
                        time.sleep(2)  # 等待播放完成
                        pygame.mixer.quit()
                        played = True
                        self.log_message("✓ 使用Pygame播放成功")
                    except Exception as e:
                        self.log_message(f"Pygame播放失败: {str(e)}")

                # 清理临时文件
                try:
                    os.unlink(wav_file)
                except:
                    pass

                if not played:
                    self.log_message("✗ 所有音频播放方法都失败了")

            except Exception as e:
                self.log_message(f"音频测试异常: {str(e)}")

        # 在新线程中播放音频
        threading.Thread(target=play_test_audio, daemon=True).start()

    def update_ui_status(self):
        """更新UI状态显示"""
        self.play_button.config(text="停止" if self.is_playing else "播放")
        self.freq_var.set(str(self.current_frequency))
        self.volume_var.set(self.current_volume)
        self.volume_label.config(text=f"{int(self.current_volume*100)}%")

def main():
    root = tk.Tk()
    app = BluetoothAudioGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("GUI已关闭")

if __name__ == '__main__':
    main()
