export const SPORT_CMD = {
    1001: "阻尼模式",
    1002: "平衡站立",
    1003: "停止移动",
    1004: "站起",
    1005: "趴下",
    1006: "恢复站立",
    1007: "欧拉角控制",
    1008: "移动",
    1009: "坐下",
    1010: "起立坐下",
    1011: "切换步态",
    1012: "触发",
    1013: "身体高度",
    1014: "抬脚高度",
    1015: "速度等级",
    1016: "打招呼",
    1017: "伸展",
    1018: "轨迹跟随",
    1019: "连续步态",
    1020: "内容",
    1021: "打滚",
    1022: "舞蹈1",
    1023: "舞蹈2",
    1024: "获取身体高度",
    1025: "获取抬脚高度",
    1026: "获取速度等级",
    1027: "切换摇杆",
    1028: "姿态",
    1029: "刮擦",
    1030: "前空翻",
    1031: "前跳",
    1032: "前扑",
    1033: "扭臀",
    1034: "获取状态",
    1035: "经济步态",
    1036: "比心",
  };
  
export const DataChannelType = {};

(function initializeDataChannelTypes(types) {
  const defineType = (r, name) => (r[name.toUpperCase()] = name.toLowerCase());

  defineType(types, "VALIDATION");
  defineType(types, "SUBSCRIBE");
  defineType(types, "UNSUBSCRIBE");
  defineType(types, "MSG");
  defineType(types, "REQUEST");
  defineType(types, "RESPONSE");
  defineType(types, "VID");
  defineType(types, "AUD");
  defineType(types, "ERR");
  defineType(types, "HEARTBEAT");
  defineType(types, "RTC_INNER_REQ");
  defineType(types, "RTC_REPORT");
  defineType(types, "ADD_ERROR");
  defineType(types, "RM_ERROR");
  defineType(types, "ERRORS");
})(DataChannelType);