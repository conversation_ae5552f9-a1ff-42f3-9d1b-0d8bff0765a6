#!/usr/bin/env python3
"""
机器狗视频流系统依赖安装脚本
自动检测并安装所需的Python包

使用方法：
python install_dependencies.py

作者：AI助手
"""

import subprocess
import sys
import importlib
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 必需的依赖包列表
REQUIRED_PACKAGES = [
    'fastapi',
    'uvicorn[standard]',
    'websockets', 
    'opencv-python',
    'numpy',
    'pillow',
    'starlette'
]

# ROS2相关包（可选，如果环境中没有ROS2）
ROS2_PACKAGES = [
    'rclpy',
    'std_msgs', 
    'geometry_msgs',
    'sensor_msgs'
]

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name.replace('-', '_'))
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装Python包"""
    try:
        logger.info(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        logger.info(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("🐕 机器狗视频流系统 - 依赖安装器")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        logger.error("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    logger.info(f"✅ Python版本: {sys.version}")
    
    # 安装基础依赖
    logger.info("开始安装基础依赖包...")
    failed_packages = []
    
    for package in REQUIRED_PACKAGES:
        package_import_name = package.split('[')[0].replace('-', '_')
        
        if check_package(package_import_name):
            logger.info(f"✅ {package} 已安装")
        else:
            if not install_package(package):
                failed_packages.append(package)
    
    # 检查ROS2依赖（可选）
    logger.info("检查ROS2依赖...")
    ros2_available = True
    
    for package in ROS2_PACKAGES:
        if not check_package(package):
            logger.warning(f"⚠️  ROS2包 {package} 未找到")
            ros2_available = False
    
    if ros2_available:
        logger.info("✅ ROS2环境检测正常")
    else:
        logger.warning("⚠️  ROS2环境未完整安装，某些功能可能不可用")
        logger.info("如需完整功能，请先安装ROS2环境")
    
    # 安装结果总结
    print("\n" + "=" * 60)
    print("📋 安装结果总结")
    print("=" * 60)
    
    if failed_packages:
        logger.error(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        logger.info("请手动安装失败的包：")
        for pkg in failed_packages:
            print(f"  pip install {pkg}")
        return False
    else:
        logger.info("✅ 所有基础依赖安装成功！")
        
    # 提供使用说明
    print("\n" + "=" * 60)
    print("🚀 使用说明")
    print("=" * 60)
    print("1. 启动机器狗服务器：")
    print("   python jiqigou2.py")
    print()
    print("2. 在浏览器中访问：")
    print("   http://localhost:8000")
    print()
    print("3. 使用WebSocket客户端测试：")
    print("   python websocket_client_example.py")
    print()
    print("4. API文档地址：")
    print("   http://localhost:8000/docs")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            logger.info("🎉 安装完成！系统已准备就绪")
        else:
            logger.error("💥 安装过程中遇到问题，请检查错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("用户中断安装")
        sys.exit(1)
    except Exception as e:
        logger.error(f"安装脚本错误: {e}")
        sys.exit(1)
