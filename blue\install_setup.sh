#!/bin/bash

echo "=== 蓝牙超声波音频控制器安装脚本 ==="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

echo "1. 更新系统包..."
apt update

echo "2. 安装系统依赖..."
apt install -y python3 python3-pip python3-dev
apt install -y bluez bluez-tools pulseaudio pulseaudio-module-bluetooth
apt install -y alsa-utils sox
apt install -y libbluetooth-dev

echo "3. 安装Python依赖..."
# 首先尝试通过apt安装系统包
apt install -y python3-flask python3-requests python3-venv

# 创建虚拟环境来安装其他依赖
echo "创建Python虚拟环境..."
sudo -u $SUDO_USER python3 -m venv /home/<USER>/bluetooth-audio-venv

# 激活虚拟环境并安装依赖
echo "在虚拟环境中安装Python包..."
sudo -u $SUDO_USER /home/<USER>/bluetooth-audio-venv/bin/pip install --upgrade pip
sudo -u $SUDO_USER /home/<USER>/bluetooth-audio-venv/bin/pip install flask==2.3.3 flask-cors==4.0.0 flasgger==******* requests==2.31.0

echo "4. 配置蓝牙服务..."
# 启用蓝牙服务
systemctl enable bluetooth
systemctl start bluetooth

# 配置PulseAudio蓝牙模块
if ! grep -q "load-module module-bluetooth-discover" /etc/pulse/system.pa; then
    echo "load-module module-bluetooth-discover" >> /etc/pulse/system.pa
fi

echo "5. 配置用户权限..."
# 将当前用户添加到audio和bluetooth组
usermod -a -G audio,bluetooth $SUDO_USER

echo "6. 创建启动脚本..."
# 创建项目目录
mkdir -p /home/<USER>/bluetooth-audio-controller
chown $SUDO_USER:$SUDO_USER /home/<USER>/bluetooth-audio-controller

# 复制项目文件到用户目录
cp *.py /home/<USER>/bluetooth-audio-controller/
cp requirements.txt /home/<USER>/bluetooth-audio-controller/
chown $SUDO_USER:$SUDO_USER /home/<USER>/bluetooth-audio-controller/*

cat > /usr/local/bin/bluetooth-audio-controller << 'EOF'
#!/bin/bash
cd /home/<USER>/bluetooth-audio-controller
/home/<USER>/bluetooth-audio-venv/bin/python bluetooth_ultrasonic_controller.py
EOF

chmod +x /usr/local/bin/bluetooth-audio-controller

cat > /usr/local/bin/bluetooth-audio-gui << 'EOF'
#!/bin/bash
cd /home/<USER>/bluetooth-audio-controller
/home/<USER>/bluetooth-audio-venv/bin/python gui_controller.py
EOF

chmod +x /usr/local/bin/bluetooth-audio-gui

echo "7. 创建systemd服务..."
cat > /etc/systemd/system/bluetooth-audio-controller.service << 'EOF'
[Unit]
Description=Bluetooth Ultrasonic Audio Controller
After=network.target bluetooth.service

[Service]
Type=simple
User=pi
WorkingDirectory=/home/<USER>/bluetooth-audio-controller
ExecStart=/home/<USER>/bluetooth-audio-venv/bin/python /home/<USER>/bluetooth-audio-controller/bluetooth_ultrasonic_controller.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

echo "安装完成！"
echo ""
echo "使用说明："
echo "1. 启动API服务器: cd ~/bluetooth-audio-controller && ~/bluetooth-audio-venv/bin/python bluetooth_ultrasonic_controller.py"
echo "2. 启动GUI界面: cd ~/bluetooth-audio-controller && ~/bluetooth-audio-venv/bin/python gui_controller.py"
echo "3. 或者使用系统命令: bluetooth-audio-controller (API服务器)"
echo "4. 或者使用系统命令: bluetooth-audio-gui (GUI界面)"
echo "5. 或者使用systemd服务: sudo systemctl enable bluetooth-audio-controller"
echo ""
echo "注意事项："
echo "- 确保蓝牙设备已配对"
echo "- 首次使用前请重启系统以应用权限更改"
echo "- API服务器运行在端口5000"
echo ""
echo "API接口文档："
echo "- POST /api/play - 开始播放 {frequency: 频率}"
echo "- POST /api/pause - 暂停播放"
echo "- POST /api/resume - 恢复播放"
echo "- POST /api/volume - 设置音量 {volume: 0.0-1.0}"
echo "- POST /api/frequency - 设置频率 {frequency: Hz}"
echo "- GET /api/bluetooth/devices - 获取蓝牙设备"
echo "- POST /api/bluetooth/connect - 连接设备 {address: MAC地址}"
echo "- POST /api/bluetooth/disconnect - 断开设备 {address: MAC地址}"
echo "- GET /api/status - 获取当前状态"
