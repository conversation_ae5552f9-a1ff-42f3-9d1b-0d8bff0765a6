"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.10.2
  Module: unitree_api.msg.dds_
  IDL file: Response_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
# import unitree_api

@dataclass
@annotate.final
@annotate.autoid("sequential")
class Response_(idl.IdlStruct, typename="unitree_api.msg.dds_.Response_"):
    header: 'unitree_sdk2py.idl.unitree_api.msg.dds_.ResponseHeader_'
    data: str
    binary: types.sequence[types.uint8]


