# Copyright (c) 2024, RoboVerse community
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
#    list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
#    this list of conditions and the following disclaimer in the documentation
#    and/or other materials provided with the distribution.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF <PERSON><PERSON><PERSON>NTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

import http.server
import socketserver
import json
import os
import sys
import asyncio
import threading
import time

# 添加python模块路径
path_to_add = os.path.abspath(os.path.join(os.path.dirname(__file__), "../python"))
if os.path.exists(path_to_add):
    sys.path.insert(0, path_to_add)
    print(f"Added {path_to_add} to sys.path")
else:
    print(f"Path {path_to_add} does not exist")

import go2_webrtc
from go2_webrtc import Go2Connection, ROBOT_CMD

PORT = 8081

# 全局变量存储机器狗连接
robot_connection = None
connection_status = "disconnected"
connected_clients = {}  # 存储已连接的客户端信息

class SDPDict:
    def __init__(self, existing_dict):
        self.__dict__["_dict"] = existing_dict

    def __getattr__(self, attr):
        try:
            return self._dict[attr]
        except KeyError:
            raise AttributeError(f"No such attribute: {attr}")

# 生成控制命令的辅助函数
def gen_command(cmd: int):
    """生成基本控制命令"""
    command = {
        "type": "msg",
        "topic": "rt/api/sport/request",
        "data": {
            "header": {"identity": {"id": Go2Connection.generate_id(), "api_id": cmd}},
            "parameter": json.dumps(cmd),
        },
    }
    return json.dumps(command)

def gen_mov_command(x: float, y: float, z: float):
    """生成移动控制命令"""
    command = {
        "type": "msg",
        "topic": "rt/api/sport/request",
        "data": {
            "header": {"identity": {"id": Go2Connection.generate_id(), "api_id": 1008}},
            "parameter": json.dumps({"x": x, "y": y, "z": z}),
        },
    }
    return json.dumps(command)

def gen_euler_command(roll: float, pitch: float, yaw: float):
    """生成姿态控制命令"""
    command = {
        "type": "msg",
        "topic": "rt/api/sport/request",
        "data": {
            "header": {"identity": {"id": Go2Connection.generate_id(), "api_id": 1007}},
            "parameter": json.dumps({"x": roll, "y": pitch, "z": yaw}),
        },
    }
    return json.dumps(command)

# 发送命令到机器狗
def send_command_to_robot(command):
    """发送命令到机器狗"""
    global robot_connection, connection_status, connected_clients
    
    # 首先尝试使用全局robot_connection
    if robot_connection and robot_connection.data_channel and robot_connection.data_channel.readyState == "open":
        try:
            robot_connection.data_channel.send(command)
            print(f"SUCCESS: Command sent to robot via global connection")
            return True
        except Exception as e:
            print(f"ERROR: Failed to send command via global connection: {str(e)}")
    
    # 如果全局连接不可用，尝试从connected_clients中找到可用的连接
    for client_ip, client_info in connected_clients.items():
        if 'connection' in client_info:
            conn = client_info['connection']
            if conn and conn.data_channel and conn.data_channel.readyState == "open":
                try:
                    conn.data_channel.send(command)
                    print(f"SUCCESS: Command sent to robot via client {client_ip} connection")
                    # 更新全局连接引用
                    robot_connection = conn
                    return True
                except Exception as e:
                    print(f"ERROR: Failed to send command via client {client_ip}: {str(e)}")
    
    print(f"ERROR: No available WebRTC connection found")
    print(f"DEBUG: Connected clients: {len(connected_clients)}")
    for client_ip, client_info in connected_clients.items():
        conn = client_info.get('connection')
        if conn:
            print(f"DEBUG: Client {client_ip} - data_channel: {conn.data_channel is not None}")
            if conn.data_channel:
                print(f"DEBUG: Client {client_ip} - readyState: {conn.data_channel.readyState}")
        else:
            print(f"DEBUG: Client {client_ip} - no connection object")
    
    return False

# 连接机器狗的异步函数
async def connect_to_robot_async(robot_ip, token=""):
    """异步连接到机器狗"""
    global robot_connection, connection_status
    
    try:
        print(f"INFO: Starting connection to robot at {robot_ip}")
        robot_connection = Go2Connection(ip=robot_ip, token=token)
        await robot_connection.connect_robot()
        
        # 等待data_channel准备好
        max_wait = 10  # 最多等待10秒
        wait_count = 0
        while wait_count < max_wait:
            if robot_connection.data_channel and robot_connection.data_channel.readyState == "open":
                connection_status = "connected"
                print(f"SUCCESS: Connected to robot at {robot_ip} and data channel is ready")
                return True
            else:
                print(f"INFO: Waiting for data channel to be ready... ({wait_count+1}/{max_wait})")
                await asyncio.sleep(1)
                wait_count += 1
        
        if wait_count >= max_wait:
            connection_status = "error: data channel not ready"
            print(f"ERROR: Data channel not ready after {max_wait} seconds")
            return False
            
    except Exception as e:
        connection_status = f"error: {str(e)}"
        print(f"ERROR: Connection failed: {str(e)}")
        return False

def connect_to_robot(robot_ip, token=""):
    """在新线程中连接机器狗"""
    def run_connect():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(connect_to_robot_async(robot_ip, token))
        finally:
            loop.close()
    
    thread = threading.Thread(target=run_connect)
    thread.daemon = True
    thread.start()
    return True


class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_OPTIONS(self):
        # Handle CORS preflight request
        self.send_response(200, "ok")
        self.send_header("Access-Control-Allow-Origin", "*")
        self.send_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
        self.send_header("Access-Control-Allow-Headers", "Content-Type")
        self.end_headers()

    def do_GET(self):
        if self.path == "/api/status":
            self.handle_status_request()
        else:
            super().do_GET()

    def do_POST(self):
        if self.path == "/offer":
            self.handle_webrtc_offer()
        elif self.path == "/api/connect":
            self.handle_connect_request()
        elif self.path == "/api/command":
            self.handle_command_request()
        elif self.path == "/api/move":
            self.handle_move_request()
        elif self.path == "/api/pose":
            self.handle_pose_request()
        elif self.path == "/api/disconnect":
            self.handle_disconnect_request()
        else:
            self.send_response(404)
            self.end_headers()

    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header("Content-Type", "application/json")
        self.send_header("Access-Control-Allow-Origin", "*")
        self.end_headers()
        self.wfile.write(json.dumps(data).encode("utf-8"))

    def get_post_data(self):
        """获取POST请求数据"""
        try:
            content_length = int(self.headers.get("Content-Length", 0))
            if content_length == 0:
                return None
            post_data = self.rfile.read(content_length)
            return json.loads(post_data.decode('utf-8'))
        except (json.JSONDecodeError, ValueError) as e:
            print(f"ERROR: Failed to parse JSON data: {e}")
            return None

    def handle_webrtc_offer(self):
        """处理WebRTC offer请求"""
        global connected_clients, robot_connection, connection_status
        
        # Read the length of the data
        content_length = int(self.headers["Content-Length"])
        # Read the incoming data
        post_data = self.rfile.read(content_length)
        # Parse the JSON data
        try:
            data = SDPDict(json.loads(post_data))
        except json.JSONDecodeError:
            self.send_response(400)
            self.end_headers()
            return

        # 获取token参数
        token = data.token if hasattr(data, 'token') and data.token else ""
        print(f"INFO: Using Token: {'提供了Token' if token else '未提供Token'}")

        # 创建Go2Connection实例来处理WebRTC连接
        try:
            robot_connection = go2_webrtc.Go2Connection(
                ip=data.ip,
                token=token,
                on_open=lambda: print(f"INFO: WebRTC data channel opened"),
                on_message=lambda msg: print(f"INFO: Received message from robot: {msg}")
            )
            print(f"INFO: Created Go2Connection instance for {data.ip}")
        except Exception as e:
            print(f"ERROR: Failed to create Go2Connection: {str(e)}")
            self.send_response(500)
            self.end_headers()
            return
        
        # 获取SDP答案
        response_data = go2_webrtc.Go2Connection.get_peer_answer(
            data, token, data.ip
        )
        
        # 存储客户端连接信息
        client_ip = data.ip
        connected_clients[client_ip] = {
            'connected_at': time.time(),
            'last_activity': time.time(),
            'connection': robot_connection  # 存储连接对象引用
        }
        print(f"INFO: WebRTC client connected from {client_ip}")
        
        # 更新连接状态
        connection_status = "webrtc_connecting"
        print(f"INFO: Connection status updated to webrtc_connecting")
        
        self.send_json_response(response_data)

    def handle_status_request(self):
        """处理状态查询请求"""
        global robot_connection, connection_status, connected_clients
        
        detailed_status = {
            'status': connection_status,
            'available_commands': list(ROBOT_CMD.keys()),
            'robot_connection_exists': robot_connection is not None,
            'data_channel_exists': False,
            'data_channel_ready': False,
            'connected_clients': len(connected_clients)
        }
        
        if robot_connection:
            detailed_status['data_channel_exists'] = robot_connection.data_channel is not None
            if robot_connection.data_channel:
                detailed_status['data_channel_ready'] = robot_connection.data_channel.readyState == "open"
                detailed_status['data_channel_state'] = robot_connection.data_channel.readyState
        
        self.send_json_response(detailed_status)

    def handle_connect_request(self):
        """处理连接请求 - 检查WebRTC连接状态"""
        global connected_clients, robot_connection, connection_status
        
        data = self.get_post_data()
        if data is None:
            self.send_json_response({'error': 'No JSON data provided'}, 400)
            return
        
        print(f"INFO: Received connect request from client")
        print(f"INFO: Current WebRTC clients: {len(connected_clients)}")
        
        # 检查是否有WebRTC客户端连接
        if len(connected_clients) > 0:
            # 检查所有客户端连接，找到可用的数据通道
            available_connections = 0
            ready_connections = 0
            
            for client_ip, client_info in connected_clients.items():
                if 'connection' in client_info:
                    conn = client_info['connection']
                    if conn and conn.data_channel:
                        available_connections += 1
                        if conn.data_channel.readyState == "open":
                            ready_connections += 1
                            # 更新全局连接引用
                            robot_connection = conn
            
            if ready_connections > 0:
                connection_status = "connected"
                print(f"SUCCESS: Platform ready for command forwarding ({ready_connections} ready connections)")
                self.send_json_response({
                    'message': 'Platform ready for command forwarding',
                    'status': 'connected',
                    'webrtc_clients': len(connected_clients),
                    'ready_connections': ready_connections
                })
                return
            elif available_connections > 0:
                print(f"WARNING: WebRTC connections exist but data channels not ready ({available_connections} total)")
                self.send_json_response({
                    'message': 'WebRTC connected but data channels not ready, please wait',
                    'status': 'connecting',
                    'webrtc_clients': len(connected_clients),
                    'available_connections': available_connections
                })
                return
        
        print(f"ERROR: No WebRTC connection available")
        self.send_json_response({
            'error': 'No WebRTC connection available. Please connect via web interface first.',
            'status': 'disconnected',
            'webrtc_clients': len(connected_clients),
            'instruction': 'Please open the web interface and connect to the robot first'
        }, 400)

    def handle_command_request(self):
        """处理命令发送请求"""
        data = self.get_post_data()
        if data is None:
            self.send_json_response({'error': 'No JSON data provided'}, 400)
            return
            
        command_name = data.get('command')
        print(f"INFO: Received command request: {command_name}")
        print(f"INFO: Full request data: {data}")
        
        if not command_name:
            self.send_json_response({'error': 'Command name is required'}, 400)
            return
        
        if command_name not in ROBOT_CMD:
            self.send_json_response({
                'error': f'Unknown command: {command_name}. Available commands: {list(ROBOT_CMD.keys())}'
            }, 400)
            return
        
        print(f"INFO: Connection status: {connection_status}")
        
        cmd_id = ROBOT_CMD[command_name]
        command = gen_command(cmd_id)
        print(f"INFO: Generated command: {command}")
        
        if send_command_to_robot(command):
            print(f"SUCCESS: Command {command_name} sent successfully")
            self.send_json_response({'message': f'Command {command_name} sent successfully'})
        else:
            print(f"ERROR: Failed to send command {command_name}")
            self.send_json_response({'error': 'Robot not connected or data channel not open'}, 400)

    def handle_move_request(self):
        """处理移动控制请求"""
        data = self.get_post_data()
        if data is None:
            self.send_json_response({'error': 'No JSON data provided'}, 400)
            return
        
        x = data.get('x', 0.0)  # 前后移动
        y = data.get('y', 0.0)  # 左右移动
        z = data.get('z', 0.0)  # 旋转
        
        # 限制数值范围
        x = max(-1.0, min(1.0, float(x)))
        y = max(-1.0, min(1.0, float(y)))
        z = max(-1.0, min(1.0, float(z)))
        
        command = gen_mov_command(x, y, z)
        
        if send_command_to_robot(command):
            self.send_json_response({
                'message': 'Move command sent successfully',
                'x': x, 'y': y, 'z': z
            })
        else:
            self.send_json_response({'error': 'Robot not connected or data channel not open'}, 400)

    def handle_pose_request(self):
        """处理姿态控制请求"""
        data = self.get_post_data()
        if data is None:
            self.send_json_response({'error': 'No JSON data provided'}, 400)
            return
        
        roll = data.get('roll', 0.0)   # 翻滚角
        pitch = data.get('pitch', 0.0) # 俯仰角
        yaw = data.get('yaw', 0.0)     # 偏航角
        
        # 限制角度范围（弧度）
        roll = max(-0.5, min(0.5, float(roll)))
        pitch = max(-0.5, min(0.5, float(pitch)))
        yaw = max(-0.5, min(0.5, float(yaw)))
        
        command = gen_euler_command(roll, pitch, yaw)
        
        if send_command_to_robot(command):
            self.send_json_response({
                'message': 'Pose command sent successfully',
                'roll': roll, 'pitch': pitch, 'yaw': yaw
            })
        else:
            self.send_json_response({'error': 'Robot not connected or data channel not open'}, 400)

    def handle_disconnect_request(self):
        """处理断开连接请求"""
        global robot_connection, connection_status
        
        if robot_connection:
            try:
                # 关闭连接
                if robot_connection.pc:
                    asyncio.run(robot_connection.pc.close())
                robot_connection = None
                connection_status = "disconnected"
                self.send_json_response({'message': 'Disconnected successfully'})
            except Exception as e:
                self.send_json_response({'error': str(e)}, 500)
        else:
            self.send_json_response({'message': 'No active connection'})


# Set up the server
with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
    print(f"Serving on port {PORT}")
    httpd.serve_forever()


