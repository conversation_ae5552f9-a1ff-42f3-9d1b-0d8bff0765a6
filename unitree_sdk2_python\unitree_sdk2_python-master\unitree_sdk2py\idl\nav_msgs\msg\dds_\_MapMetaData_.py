"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.11.0
  Module: nav_msgs.msg.dds_
  IDL file: MapMetaData_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
# import nav_msgs

# if TYPE_CHECKING:
#     import builtin_interfaces.msg.dds_
#     import geometry_msgs.msg.dds_



@dataclass
@annotate.final
@annotate.autoid("sequential")
class MapMetaData_(idl.IdlStruct, typename="nav_msgs.msg.dds_.MapMetaData_"):
    map_load_time: 'unitree_sdk2py.idl.builtin_interfaces.msg.dds_.Time_'
    resolution: types.float32
    width: types.uint32
    height: types.uint32
    origin: 'unitree_sdk2py.idl.geometry_msgs.msg.dds_.Pose_'

