<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Unitree Go2 WebRTC 控制中心</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      :root {
        /* 科技感配色方案 */
        --primary-color: #00ffff;
        --secondary-color: #0088ff;
        --accent-color: #ff3366;
        --success-color: #00ff88;
        --warning-color: #ffcc00;
        --error-color: #ff4466;
        --info-color: #66ccff;
        
        /* 背景渐变 */
        --bg-primary: #0c0c14;
        --bg-secondary: #1a1a2e;
        --bg-tertiary: #16213e;
        --bg-quaternary: #2d3748;
        
        /* 文本颜色 */
        --text-primary: #ffffff;
        --text-secondary: #a0aec0;
        --text-tertiary: #718096;
        --text-accent: #00ffff;
        
        /* 边框和玻璃效果 */
        --border-color: rgba(0, 255, 255, 0.3);
        --border-secondary: rgba(0, 136, 255, 0.2);
        --glass-bg: rgba(26, 26, 46, 0.7);
        --glass-border: rgba(0, 255, 255, 0.15);
        
        /* 阴影效果 */
        --shadow-primary: 0 8px 32px rgba(0, 255, 255, 0.2);
        --shadow-secondary: 0 4px 16px rgba(0, 0, 0, 0.4);
        --shadow-neon: 0 0 20px rgba(0, 255, 255, 0.5);
        --shadow-accent: 0 0 15px rgba(255, 51, 102, 0.4);
        
        /* 大屏适配 */
        --container-max-width: 1920px;
        --panel-gap: 2rem;
        --panel-padding: 2rem;
        --border-radius: 16px;
        --border-radius-lg: 24px;
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background: 
          radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 40%),
          radial-gradient(circle at 80% 20%, rgba(255, 51, 102, 0.08) 0%, transparent 40%),
          radial-gradient(circle at 40% 40%, rgba(0, 136, 255, 0.06) 0%, transparent 40%),
          linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
        background-attachment: fixed;
        color: var(--text-primary);
        min-height: 100vh;
        overflow-x: hidden;
        position: relative;
        /* 禁用拖动和选择 */
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-user-drag: none;
         -khtml-user-drag: none;
         -moz-user-drag: none;
         -o-user-drag: none;
         -webkit-touch-callout: none;
         -webkit-tap-highlight-color: transparent;
      }

      /* 全局禁用拖动和选择 */
      * {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
      }
      
      img {
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        pointer-events: none;
      }

      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: 
          radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
      }

      .header {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        border-bottom: 1px solid var(--border-color);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        padding: 1.5rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
        animation: slideInDown 0.8s ease-out;
      }

      .header-content {
        max-width: var(--container-max-width);
        margin: 0 auto;
        padding: 0 var(--panel-gap);
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-accent);
        text-shadow: 0 0 20px var(--primary-color);
      }

      .logo i {
        font-size: 2.5rem;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: pulse 3s ease-in-out infinite;
        filter: drop-shadow(0 0 10px var(--primary-color));
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        background: rgba(26, 26, 46, 0.8);
        border: 1px solid var(--border-color);
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: var(--shadow-secondary);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
      }

      .status-indicator:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-neon);
      }

      .device-status-panel {
        display: flex;
        gap: 1rem;
        padding: 0.5rem 1rem;
        background: rgba(26, 26, 46, 0.8);
        border: 1px solid var(--border-color);
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        backdrop-filter: blur(15px);
        transition: all 0.3s ease;
        opacity: 0.8;
      }

      .device-status-panel:hover {
        opacity: 1;
        transform: translateY(-1px);
      }

      .device-info,
      .network-info {
        display: flex;
        align-items: center;
        gap: 0.4rem;
        color: var(--text-secondary);
      }

      .device-info i,
      .network-info i {
        color: var(--primary-color);
        font-size: 0.9rem;
      }

      .device-info.mobile i {
        color: var(--accent-color);
      }

      .network-info.poor i {
        color: var(--error-color);
      }

             .network-info.good i {
         color: var(--success-color);
       }

       /* 键盘控制悬浮窗样式 */
       .keyboard-control-window {
         position: fixed;
         top: 100px;
         right: 20px;
         width: 420px;
         background: var(--glass-bg);
         backdrop-filter: blur(25px);
         border: 1px solid var(--border-color);
         border-radius: var(--border-radius-lg);
         box-shadow: var(--shadow-primary);
         z-index: 1000;
         animation: slideInRight 0.5s ease-out;
         font-family: 'Inter', sans-serif;
       }

       .keyboard-window-header {
         display: flex;
         justify-content: space-between;
         align-items: center;
         padding: 1rem 1.5rem;
         background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
         color: var(--bg-primary);
         border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
         cursor: move;
       }

       .keyboard-window-title {
         display: flex;
         align-items: center;
         gap: 0.5rem;
         font-weight: 700;
         font-size: 1rem;
       }

       .keyboard-window-controls {
         display: flex;
         gap: 0.5rem;
       }

       .window-control-btn {
         width: 28px;
         height: 28px;
         border: none;
         border-radius: 6px;
         background: rgba(255, 255, 255, 0.2);
         color: white;
         cursor: pointer;
         display: flex;
         align-items: center;
         justify-content: center;
         transition: all 0.2s ease;
         font-size: 0.75rem;
       }

       .window-control-btn:hover {
         background: rgba(255, 255, 255, 0.3);
         transform: scale(1.1);
       }

       .window-control-btn.close:hover {
         background: var(--error-color);
       }

       .keyboard-window-content {
         padding: 1.5rem;
       }

       .keyboard-status {
         display: flex;
         justify-content: space-between;
         margin-bottom: 1.5rem;
         font-size: 0.85rem;
       }

       .connection-status,
       .control-mode {
         display: flex;
         align-items: center;
         gap: 0.5rem;
         color: var(--text-secondary);
       }

       .connection-status.connected {
         color: var(--success-color);
       }

       .control-instructions h4 {
         color: var(--text-accent);
         margin-bottom: 1rem;
         font-size: 1rem;
         display: flex;
         align-items: center;
         gap: 0.5rem;
       }

       .control-instructions h4::before {
         content: '🎮';
         font-size: 1.2rem;
       }

       .key-mappings {
         display: grid;
         grid-template-columns: 1fr 1fr 1fr;
         gap: 0.5rem;
         margin-bottom: 1.5rem;
       }

       .key-mapping {
         display: flex;
         align-items: center;
         gap: 0.75rem;
         padding: 0.5rem;
         background: rgba(26, 26, 46, 0.4);
         border-radius: 8px;
         transition: all 0.3s ease;
       }

       .key-mapping.active {
         background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
         color: var(--bg-primary);
         box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
       }

       .key-mapping kbd {
         display: inline-block;
         padding: 0.25rem 0.5rem;
         background: var(--bg-secondary);
         border: 1px solid var(--border-color);
         border-radius: 4px;
         font-family: 'Courier New', monospace;
         font-size: 0.9rem;
         font-weight: bold;
         color: var(--text-accent);
         min-width: 28px;
         text-align: center;
         box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
       }

       .key-mapping.active kbd {
         background: var(--bg-primary);
         color: var(--primary-color);
         box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
       }

       .key-mapping span {
         flex: 1;
         font-size: 0.85rem;
         color: var(--text-secondary);
       }

       .key-mapping.active span {
         color: white;
       }

       .key-status {
        width: 8px;
        height: 8px;
         border-radius: 50%;
         background: var(--text-tertiary);
         transition: all 0.3s ease;
       }

       .key-mapping.active .key-status {
         background: var(--bg-primary);
         box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
       }

       .control-settings {
         background: rgba(26, 26, 46, 0.3);
         border-radius: 8px;
         padding: 1rem;
         margin-bottom: 1rem;
       }

       .speed-control,
       .turn-control {
         display: flex;
         align-items: center;
         gap: 0.75rem;
         margin-bottom: 0.75rem;
       }

       .turn-control {
         margin-bottom: 0;
       }

       .speed-control label,
       .turn-control label {
         min-width: 80px;
         font-size: 0.85rem;
         color: var(--text-secondary);
         font-weight: 500;
       }

       .speed-control input,
       .turn-control input {
         flex: 1;
         height: 4px;
         background: rgba(255, 255, 255, 0.2);
         border-radius: 2px;
         outline: none;
         cursor: pointer;
         -webkit-appearance: none;
       }

       .speed-control input::-webkit-slider-thumb,
       .turn-control input::-webkit-slider-thumb {
         -webkit-appearance: none;
         appearance: none;
         width: 16px;
         height: 16px;
         border-radius: 50%;
         background: var(--primary-color);
         cursor: pointer;
         box-shadow: 0 2px 6px rgba(0, 255, 255, 0.4);
       }

       .speed-control span,
       .turn-control span {
         min-width: 40px;
         text-align: right;
         font-size: 0.85rem;
         color: var(--text-accent);
         font-weight: 600;
       }

       .control-feedback {
         background: rgba(0, 0, 0, 0.3);
         border-radius: 8px;
         padding: 0.75rem;
         border-left: 3px solid var(--primary-color);
       }

       .current-command {
         display: flex;
         align-items: center;
         gap: 0.75rem;
         font-size: 0.85rem;
       }

       .current-command span {
         color: var(--text-secondary);
       }

       .current-command code {
         background: var(--bg-secondary);
         color: var(--text-accent);
         padding: 0.25rem 0.5rem;
         border-radius: 4px;
         font-family: 'Courier New', monospace;
         font-size: 0.8rem;
         border: 1px solid var(--border-secondary);
       }

       /* 最小化状态样式 */
       .keyboard-control-minimized {
         position: fixed;
         bottom: 20px;
         right: 20px;
         background: var(--glass-bg);
         backdrop-filter: blur(20px);
         border: 1px solid var(--border-color);
         border-radius: 12px;
         padding: 0.75rem 1rem;
         cursor: pointer;
         transition: all 0.3s ease;
         z-index: 999;
         min-width: 150px;
       }

       .keyboard-control-minimized:hover {
         transform: translateY(-2px);
         box-shadow: var(--shadow-neon);
       }

       .minimized-content {
         display: flex;
         align-items: center;
         gap: 0.5rem;
         color: var(--text-secondary);
         font-size: 0.85rem;
       }

       .minimized-content i {
         color: var(--primary-color);
       }

       .active-keys {
         display: flex;
         gap: 0.25rem;
         margin-left: auto;
       }

       .active-keys .active-key {
         background: var(--primary-color);
         color: var(--bg-primary);
         padding: 0.125rem 0.25rem;
         border-radius: 3px;
         font-size: 0.7rem;
         font-weight: bold;
       }

       /* 动画效果 */
       @keyframes slideInRight {
         from {
           opacity: 0;
           transform: translateX(100%);
         }
         to {
           opacity: 1;
           transform: translateX(0);
         }
       }

       /* 移动端隐藏键盘控制 */
       @media (max-width: 768px) {
         .keyboard-control-window,
         .keyboard-control-minimized {
           display: none !important;
         }
       }

      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: var(--error-color);
        animation: statusPulse 2s infinite;
        box-shadow: 0 0 10px currentColor;
      }

      .status-dot.connected {
        background: var(--success-color);
        animation: connectedPulse 3s infinite;
      }

      @keyframes statusPulse {
        0%, 100% { 
          opacity: 1; 
          transform: scale(1);
          box-shadow: 0 0 10px currentColor;
        }
        50% { 
          opacity: 0.6; 
          transform: scale(1.2);
          box-shadow: 0 0 20px currentColor;
        }
      }

      @keyframes connectedPulse {
        0%, 100% { 
          opacity: 1; 
          transform: scale(1);
          box-shadow: 0 0 15px var(--success-color);
        }
        50% { 
          opacity: 0.8; 
          transform: scale(1.1);
          box-shadow: 0 0 25px var(--success-color);
        }
      }

      .main-container {
        max-width: var(--container-max-width);
        margin: 0 auto;
        padding: var(--panel-gap);
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        grid-template-rows: auto auto 1fr;
        gap: var(--panel-gap);
        min-height: calc(100vh - 120px);
        animation: fadeInUp 1s ease-out;
      }

      .dashboard-grid {
        display: contents;
      }

      .control-section {
        grid-column: 1;
        grid-row: 1 / span 2;
        display: flex;
        flex-direction: column;
        gap: var(--panel-gap);
      }

      .video-section {
        grid-column: 2;
        grid-row: 1 / span 3;
      }

      .audio-section {
        grid-column: 3;
        grid-row: 1 / span 2;
        display: flex;
        flex-direction: column;
        gap: var(--panel-gap);
      }

      .command-section {
        grid-column: 1 / span 3;
        grid-row: 3;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--panel-gap);
      }

      .panel {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        padding: var(--panel-padding);
        box-shadow: var(--shadow-primary);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
      }

      .panel::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .panel:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-neon), var(--shadow-secondary);
        border-color: var(--primary-color);
      }

      .panel:hover::before {
        opacity: 1;
      }

      .control-panel {
        composes: panel;
      }

      .video-panel {
        composes: panel;
        display: flex;
        flex-direction: column;
        min-height: 600px;
      }

      .audio-panel {
        composes: panel;
      }

      .panel-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 2rem;
        color: var(--text-accent);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        text-shadow: 0 0 10px var(--primary-color);
        position: relative;
        padding-bottom: 0.75rem;
      }

      .panel-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        box-shadow: 0 0 10px var(--primary-color);
      }

      .panel-title i {
        font-size: 1.6rem;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 0 5px var(--primary-color));
      }

      .input-group {
        margin-bottom: 2rem;
      }

      .input-group label {
        display: block;
        margin-bottom: 0.75rem;
        font-weight: 600;
        color: var(--text-accent);
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
      }

      .input-group label i {
        margin-right: 0.5rem;
        color: var(--primary-color);
      }

      .input-field {
        width: 100%;
        padding: 1rem 1.25rem;
        background: rgba(26, 26, 46, 0.6);
        border: 1px solid var(--border-secondary);
        border-radius: var(--border-radius);
        color: var(--text-primary);
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(15px);
        position: relative;
      }

      .input-field:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2), 0 0 20px rgba(0, 255, 255, 0.1);
        background: rgba(26, 26, 46, 0.8);
        transform: translateY(-2px);
      }

      .input-field:hover {
        border-color: var(--primary-color);
      }

      .input-field::placeholder {
        color: var(--text-secondary);
      }

      select.input-field {
        cursor: pointer;
      }

      select.input-field option {
        background: var(--bg-secondary);
        color: var(--text-primary);
        padding: 0.5rem;
        border: none;
      }

      select.input-field option:hover {
        background: var(--bg-tertiary);
      }

      select.input-field option:checked {
        background: var(--primary-color);
        color: var(--bg-primary);
      }

      .button-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      @media (max-width: 768px) {
        .button-group {
          grid-template-columns: 1fr;
        gap: 1rem;
        }
      }

      .btn {
        padding: 1rem 2rem;
        border: none;
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
      }

      .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
      }

      .btn:hover::before {
        left: 100%;
      }

      .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: var(--bg-primary);
        box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
      }

      .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 255, 255, 0.4), 0 0 20px rgba(0, 255, 255, 0.2);
        border-color: var(--primary-color);
      }

      .btn-secondary {
        background: linear-gradient(135deg, var(--accent-color), #ff4466);
        color: white;
        box-shadow: 0 8px 25px rgba(255, 51, 102, 0.3);
        border: 1px solid rgba(255, 51, 102, 0.2);
      }

      .btn-secondary:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(255, 51, 102, 0.4), 0 0 20px rgba(255, 51, 102, 0.2);
        border-color: var(--accent-color);
      }

      .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        pointer-events: none;
      }
      
      /* 移动端按钮优化 */
      @media (max-width: 768px) {
        .btn {
          padding: 1rem 1.5rem;
          font-size: 1.1rem;
          min-height: 48px;
          touch-action: manipulation;
        }
        
        .btn:not(:disabled) {
          cursor: pointer;
          -webkit-tap-highlight-color: transparent;
        }
      }

      .video-container {
        flex: 1;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(26, 26, 46, 0.6));
        border: 1px solid var(--border-secondary);
        border-radius: var(--border-radius);
        overflow: hidden;
        position: relative;
        min-height: 500px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        box-shadow: inset 0 0 50px rgba(0, 255, 255, 0.05);
      }

      .video-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
          linear-gradient(45deg, transparent 49%, rgba(0, 255, 255, 0.03) 50%, transparent 51%),
          linear-gradient(-45deg, transparent 49%, rgba(0, 255, 255, 0.03) 50%, transparent 51%);
        background-size: 20px 20px;
        opacity: 0.3;
        pointer-events: none;
      }

      #video-frame {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 16px;
        background: #000;
      }
      
      /* 移动端视频优化 */
      @media (max-width: 768px) {
        #video-frame {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
        
        .video-container {
          min-height: 250px;
          max-height: 60vh;
        }
      }

      .video-placeholder {
        color: var(--text-secondary);
        text-align: center;
        font-size: 1.2rem;
        font-weight: 500;
        z-index: 2;
        position: relative;
      }

      .video-placeholder i {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        opacity: 0.6;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.3));
      }

      .video-placeholder div {
        margin: 0.5rem 0;
      }

      .custom-command {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 1.5rem;
        margin-top: 2rem;
        box-shadow: var(--shadow-secondary);
      }

      .custom-command-input {
        display: flex;
        gap: 1.5rem;
        align-items: flex-end;
        flex-wrap: wrap;
      }

      .custom-command-input .input-group {
        flex: 1;
        margin-bottom: 0;
        min-width: 300px;
      }

      @media (max-width: 768px) {
        .custom-command-input {
          flex-direction: column;
          align-items: stretch;
        }
        
        .custom-command-input .input-group {
          min-width: auto;
        }
      }

      .log-container {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 1.5rem;
        margin-top: 2rem;
        box-shadow: var(--shadow-secondary);
      }

      .log {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(26, 26, 46, 0.4));
        border: 1px solid var(--border-secondary);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        max-height: 400px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        line-height: 1.6;
        color: var(--text-secondary);
        backdrop-filter: blur(10px);
        box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.05);
      }

      .log-code {
        white-space: pre-wrap;
        word-break: break-word;
      }

      .corner-div {
         position: fixed;
         width: 120px;
         height: 120px;
         background: var(--glass-bg);
         backdrop-filter: blur(20px);
         border: 1px solid var(--glass-border);
         border-radius: 50%;
         display: none; /* 默认隐藏，通过按钮控制 */
         justify-content: center;
         align-items: center;
         z-index: 50;
         transition: all 0.3s ease;
         opacity: 0.8;
         touch-action: manipulation;
         user-select: none;
         -webkit-user-select: none;
         -webkit-touch-callout: none;
       }

       .corner-div.active {
         display: flex; /* 激活时显示 */
       }

       .corner-div:hover {
         opacity: 1;
         transform: scale(1.05);
       }
       
       .corner-div canvas {
           border-radius: 50%;
           touch-action: none;
           pointer-events: auto;
           position: relative;
         }

        /* 操控模式选择按钮样式 */
         .control-mode-button {
           position: fixed;
           bottom: 20px;
           right: 20px;
           padding: 12px 20px;
           background: var(--glass-bg);
           backdrop-filter: blur(20px);
           border: 1px solid var(--glass-border);
           border-radius: 25px;
           display: flex;
           align-items: center;
           gap: 8px;
           z-index: 100;
           cursor: pointer;
           transition: all 0.3s ease;
           opacity: 0.9;
           color: var(--primary-color);
           font-size: 0.9rem;
           font-weight: 500;
           box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
         }

         .control-mode-button:hover {
           opacity: 1;
           transform: translateY(-2px);
           box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
         }

         /* 操控模式选择弹窗样式 */
         .control-mode-modal {
           position: fixed;
           top: 0;
           left: 0;
           width: 100%;
           height: 100%;
           background: rgba(0, 0, 0, 0.8);
           backdrop-filter: blur(10px);
           z-index: 1000;
           display: flex;
           justify-content: center;
           align-items: center;
         }

         .modal-content {
           background: var(--glass-bg);
           backdrop-filter: blur(20px);
           border: 1px solid var(--glass-border);
           border-radius: 20px;
           padding: 30px;
           max-width: 400px;
           width: 90%;
           position: relative;
           box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
         }

         .modal-content h3 {
           text-align: center;
           margin-bottom: 25px;
           color: var(--text-primary);
           font-size: 1.5rem;
         }

         .mode-options {
           display: flex;
           flex-direction: column;
           gap: 15px;
         }

         .mode-option {
           background: var(--glass-bg);
           border: 1px solid var(--glass-border);
           border-radius: 15px;
           padding: 20px;
           cursor: pointer;
           transition: all 0.3s ease;
           text-align: left;
           color: var(--text-primary);
           display: flex;
           align-items: center;
           gap: 15px;
         }

         .mode-option:hover {
           background: var(--primary-color);
           transform: translateY(-2px);
           box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
         }

         .mode-option i {
           font-size: 2rem;
           color: var(--primary-color);
         }

         .mode-option:hover i {
           color: white;
         }

         .mode-option span {
           font-size: 1.1rem;
           font-weight: 600;
           display: block;
         }

         .mode-option small {
           font-size: 0.85rem;
           opacity: 0.8;
           display: block;
           margin-top: 4px;
         }

         .close-modal-btn {
           position: absolute;
           top: 15px;
           right: 15px;
           background: none;
           border: none;
           color: var(--text-secondary);
           font-size: 1.2rem;
           cursor: pointer;
           padding: 5px;
           border-radius: 50%;
           transition: all 0.3s ease;
         }

         .close-modal-btn:hover {
           color: var(--error-color);
           background: rgba(255, 71, 87, 0.1);
         }

         /* 手机操控模式全屏布局 */
         .mobile-control-layout {
           position: fixed;
           top: 0;
           left: 0;
           width: 100%;
           height: 100%;
           background: var(--bg-primary);
           z-index: 999;
           display: flex;
           flex-direction: column;
           touch-action: none;
           user-select: none;
           -webkit-user-select: none;
           -webkit-touch-callout: none;
           -webkit-user-drag: none;
           -khtml-user-drag: none;
           -moz-user-drag: none;
           -o-user-drag: none;
         }

         .mobile-video-area {
           flex: 1;
           position: relative;
           background: #000;
           display: flex;
           justify-content: center;
           align-items: center;
         }

         .mobile-video-area video {
           width: 100%;
           height: 100%;
           object-fit: contain;
         }

         .mobile-video-placeholder {
           position: absolute;
           top: 0;
           left: 0;
           width: 100%;
           height: 100%;
           display: flex;
           flex-direction: column;
           justify-content: center;
           align-items: center;
           text-align: center;
           color: var(--text-secondary);
           background: var(--bg-primary);
           z-index: 1;
         }

         .mobile-video-placeholder i {
           font-size: 3rem;
           margin-bottom: 1rem;
           opacity: 0.5;
         }

         .exit-mobile-btn {
           position: absolute;
           top: 20px;
           right: 20px;
           background: rgba(0, 0, 0, 0.7);
           border: none;
           color: white;
           width: 40px;
           height: 40px;
           border-radius: 50%;
           cursor: pointer;
           font-size: 1.2rem;
           transition: all 0.3s ease;
         }

         .exit-mobile-btn:hover {
           background: var(--error-color);
         }

         .mobile-joystick-area {
           height: 50%;
           background: var(--bg-secondary);
           position: relative;
           display: flex;
           justify-content: space-around;
           align-items: center;
           padding: 30px 40px;
           box-sizing: border-box;
           touch-action: none;
           user-select: none;
           -webkit-user-select: none;
           -webkit-touch-callout: none;
           -webkit-user-drag: none;
           -khtml-user-drag: none;
           -moz-user-drag: none;
           -o-user-drag: none;
         }

         .mobile-joystick {
           width: 160px;
           height: 160px;
           background: var(--glass-bg);
           backdrop-filter: blur(20px);
           border: 2px solid var(--glass-border);
           border-radius: 50%;
           position: relative;
           touch-action: none;
           user-select: none;
           -webkit-user-select: none;
           -webkit-touch-callout: none;
           -webkit-user-drag: none;
           -khtml-user-drag: none;
           -moz-user-drag: none;
           -o-user-drag: none;
           box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
           transition: all 0.2s ease;
         }

         .mobile-joystick:active {
           transform: scale(0.95);
           box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
         }

         /* 速度控制面板样式 */
         .speed-control-panel {
           position: absolute;
           top: 20px;
           left: 50%;
           transform: translateX(-50%);
           background: var(--glass-bg);
           backdrop-filter: blur(20px);
           border: 1px solid var(--glass-border);
           border-radius: 15px;
           padding: 15px 20px;
           min-width: 280px;
           z-index: 10;
           box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
         }

         .speed-control-header {
           display: flex;
           align-items: center;
           justify-content: space-between;
           margin-bottom: 10px;
           color: var(--text-primary);
           font-size: 0.9rem;
           font-weight: 500;
         }

         .speed-control-header i {
           color: var(--primary-color);
           margin-right: 8px;
         }

         .speed-control-header span:last-child {
           color: var(--primary-color);
           font-weight: 600;
           min-width: 40px;
           text-align: right;
         }

         .speed-slider {
           width: 100%;
           height: 6px;
           border-radius: 3px;
           background: rgba(255, 255, 255, 0.2);
           outline: none;
           -webkit-appearance: none;
           appearance: none;
           margin: 8px 0;
         }

         .speed-slider::-webkit-slider-thumb {
           -webkit-appearance: none;
           appearance: none;
           width: 20px;
           height: 20px;
           border-radius: 50%;
           background: var(--primary-color);
           cursor: pointer;
           box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
           transition: all 0.2s ease;
         }

         .speed-slider::-webkit-slider-thumb:hover {
           transform: scale(1.2);
           box-shadow: 0 4px 12px rgba(0, 212, 255, 0.6);
         }

         .speed-slider::-moz-range-thumb {
           width: 20px;
           height: 20px;
           border-radius: 50%;
           background: var(--primary-color);
           cursor: pointer;
           border: none;
           box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
         }

         .speed-labels {
           display: flex;
           justify-content: space-between;
           color: var(--text-secondary);
           font-size: 0.75rem;
           margin-top: 5px;
         }

         .mobile-joystick i {
           position: absolute;
           top: 50%;
           left: 50%;
           transform: translate(-50%, -50%);
           font-size: 2.2rem;
           pointer-events: none;
           z-index: 3;
         }

         .left-joystick i {
           color: var(--primary-color);
         }

         .right-joystick i {
           color: var(--accent-color);
         }

         /* 摇杆Canvas样式 */
         .mobile-joystick canvas {
           position: absolute;
           top: 0;
           left: 0;
           width: 100% !important;
           height: 100% !important;
           border-radius: 50%;
           z-index: 1;
           touch-action: none;
         }

       .bottom-left {
         bottom: 2rem;
         left: 2rem;
       }

       .bottom-right {
         bottom: 2rem;
         right: 2rem;
       }

       /* 虚拟摇杆现在完全由切换按钮控制，移除自动显示的媒体查询 */

      /* 大屏优化 */
      @media (min-width: 1920px) {
        :root {
          --panel-gap: 3rem;
          --panel-padding: 3rem;
        }
        
        .panel-title {
          font-size: 1.6rem;
        }
        
        .logo {
          font-size: 2rem;
        }
        
        .logo i {
          font-size: 3rem;
        }
      }

      /* 中等屏幕适配 */
      @media (max-width: 1600px) {
        .main-container {
          grid-template-columns: 1fr 1.5fr 1fr;
        }
      }

      /* 平板适配 */
      @media (max-width: 1200px) {
        .main-container {
          grid-template-columns: 1fr;
          grid-template-rows: auto;
        }
        
        .control-section,
        .video-section,
        .audio-section {
          grid-column: 1;
          grid-row: auto;
        }
        
        .command-section {
          grid-column: 1;
          grid-template-columns: 1fr;
        }
        
        .video-panel {
          min-height: 400px;
        }
      }

      /* 移动端优化 */
      @media (max-width: 768px) {
        :root {
          --panel-gap: 1rem;
          --panel-padding: 1.5rem;
        }
        
        .header-content {
          padding: 0 1rem;
        }
        
        .logo {
          font-size: 1.25rem;
        }
        
        .logo i {
          font-size: 1.8rem;
        }
        
        .panel-title {
          font-size: 1.2rem;
        }
        
        .volume-control {
          flex-direction: column;
          align-items: stretch !important;
          gap: 0.5rem !important;
        }
        
        .volume-control label {
          min-width: auto !important;
          text-align: center;
        }
        
        .volume-slider {
          height: 8px !important;
          margin: 0.5rem 0 !important;
        }
        
        .volume-slider::-webkit-slider-thumb {
          width: 24px !important;
          height: 24px !important;
        }
        
        .volume-slider::-moz-range-thumb {
          width: 24px !important;
          height: 24px !important;
        }
        
        .audio-control-options {
          gap: 1.5rem !important;
        }
        
        .video-container {
          min-height: 250px;
        }
      }

      @media (max-width: 768px) {
         .button-group {
           grid-template-columns: 1fr;
         }
         
         .custom-command-input {
           flex-direction: column;
         }
         
         .corner-div {
           width: 80px;
           height: 80px;
           bottom: 1rem;
         }
         
         .bottom-left {
           left: 1rem;
         }
         
         .bottom-right {
           right: 1rem;
         }
       }

      @media (max-width: 480px) {
        .main-container {
          padding: 0.5rem;
        }
        
        .header-content {
          flex-direction: column;
          gap: 1rem;
          text-align: center;
        }
        
        .device-status-panel {
          display: none !important;
        }
        
        .panel {
          padding: 1rem;
        }
      }

      /* 科技感动画效果 */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(40px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      @keyframes slideInDown {
        from {
          opacity: 0;
          transform: translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%, 100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.8;
          transform: scale(1.05);
        }
      }

      @keyframes scanline {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100vw);
        }
      }

      /* 扫描线效果 */
      .scanline {
        position: fixed;
        top: 0;
        left: 0;
        width: 2px;
        height: 100vh;
        background: linear-gradient(to bottom, 
          transparent, 
          var(--primary-color), 
          transparent);
        animation: scanline 8s linear infinite;
        opacity: 0.3;
        z-index: -1;
      }

      .panel {
        animation: fadeInUp 0.8s ease-out;
      }

      .panel:nth-child(1) { animation-delay: 0.1s; }
      .panel:nth-child(2) { animation-delay: 0.2s; }
      .panel:nth-child(3) { animation-delay: 0.3s; }
      .panel:nth-child(4) { animation-delay: 0.4s; }

      /* 定制滚动条 */
      .log::-webkit-scrollbar {
        width: 8px;
      }

      .log::-webkit-scrollbar-track {
        background: rgba(26, 26, 46, 0.5);
        border-radius: 4px;
      }

      .log::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        border-radius: 4px;
        box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
      }

      .log::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      /* 全局滚动条 */
      ::-webkit-scrollbar {
        width: 12px;
      }

      ::-webkit-scrollbar-track {
        background: var(--bg-secondary);
      }

      ::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        border-radius: 6px;
        border: 2px solid var(--bg-secondary);
      }

      ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(45deg, var(--secondary-color), var(--accent-color));
      }

      /* 语音发送功能样式 */
      .audio-panel {
        margin-top: 1.5rem;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 16px;
        padding: 1.25rem;
        border: 1px solid var(--glass-border);
      }
      
      /* 录音功能区域样式 */
      .voice-recording-section {
        margin-top: 1.5rem;
        padding: 1rem;
        background: rgba(0, 212, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(0, 212, 255, 0.2);
      }
      
      .recording-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
      }
      
      .record-btn {
        flex: 1;
        padding: 1rem 1.5rem;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
        min-height: 60px;
      }
      
      .record-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }
      
      .record-btn:hover::before {
        left: 100%;
      }
      
      .record-btn:not(.stop-btn) {
        background: linear-gradient(45deg, var(--success-color), #00cc66);
        color: white;
        box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
      }
      
      .record-btn:not(.stop-btn):hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
      }
      
      .record-btn.stop-btn {
        background: linear-gradient(45deg, var(--error-color), #ff6b6b);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
        animation: pulse 2s infinite;
      }
      
      .record-btn.stop-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
      }
      
      .record-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        animation: none !important;
      }
      
      .record-btn i {
        font-size: 1.2rem;
      }
      
      .recording-tips {
        font-size: 0.85rem;
        color: var(--text-secondary);
        line-height: 1.5;
      }
      
      .recording-tips p {
        margin: 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .recording-tips i {
        color: var(--primary-color);
        min-width: 16px;
      }
      
      .test-connection-btn {
        margin-top: 0.5rem;
        padding: 0.5rem 1rem;
        border: 1px solid var(--primary-color);
        background: transparent;
        color: var(--primary-color);
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .test-connection-btn:hover {
        background: var(--primary-color);
        color: white;
      }
      
      .tips-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }
      
      .tips-buttons .test-connection-btn {
        flex: 1;
        min-width: 120px;
      }
      
      .mic-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 0.75rem;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        font-size: 0.9rem;
      }
      
      .mic-level-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
      }
      
      .mic-level-bar {
        flex: 1;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
      }
      
      .mic-level-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
        width: 0%;
        transition: width 0.1s ease;
      }
      

      
      .audio-log {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        padding: 0.75rem;
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        height: 80px;
        overflow-y: auto;
      }
      
      #audio-log-title {
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
      }
      
      #audio-log-content {
        line-height: 1.4;
      }

      /* 机器狗音频播放功能样式 */
      .robot-audio-section {
        margin-top: 1.5rem;
        padding: 1rem;
        background: rgba(255, 107, 53, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 107, 53, 0.2);
      }

      .audio-control-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .audio-control-btn {
        flex: 1;
        padding: 1rem 1.5rem;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;
        min-height: 60px;
      }

      .audio-control-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      .audio-control-btn:hover::before {
        left: 100%;
      }

      .audio-control-btn.start-btn {
        background: linear-gradient(45deg, var(--accent-color), #ff8c42);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
      }

      .audio-control-btn.start-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
      }

      .audio-control-btn.stop-btn {
        background: linear-gradient(45deg, var(--error-color), #ff6b6b);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
        animation: pulse 2s infinite;
      }

      .audio-control-btn.stop-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
      }

      .audio-control-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        animation: none !important;
      }

      .robot-audio-status {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 1rem;
        padding: 0.75rem;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        font-size: 0.9rem;
      }

      .audio-status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--accent-color);
      }

      .audio-level-bar {
        flex: 1;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
      }

      .audio-level-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--accent-color), var(--warning-color));
        width: 0%;
        transition: width 0.1s ease;
      }

      .audio-control-options {
        margin-top: 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .volume-control {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
      }

      .volume-control label {
        font-size: 0.9rem;
        color: var(--text-secondary);
        min-width: 80px;
      }

      .volume-slider {
        flex: 1;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        outline: none;
        cursor: pointer;
        -webkit-appearance: none;
      }

      .volume-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: var(--accent-color);
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      }

      .volume-slider::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: var(--accent-color);
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      }

      .audio-info {
        font-size: 0.85rem;
        color: var(--text-secondary);
        line-height: 1.5;
      }

      .audio-info p {
        margin: 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .audio-info i {
        color: var(--accent-color);
        min-width: 16px;
      }

      .info-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 0.5rem;
      }

      #robot-audio-log-title {
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
      }

      #robot-audio-log-content {
        line-height: 1.4;
      }
    </style>
    <script src="md5.js"></script>
    <script src="joy.min.js"></script>
<script src="mobile-joy.js"></script>
  </head>
  <body>
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <i class="fas fa-robot"></i>
          <span>Unitree Go2 控制中心</span>
        </div>
        <div class="status-indicator">
          <div class="status-dot" id="status-dot"></div>
          <span id="status-text">未连接</span>
        </div>
        
        <!-- 设备状态面板 -->
        <div class="device-status-panel" id="device-status-panel" style="display: none;">
          <div class="device-info">
            <i class="fas fa-mobile-alt" id="device-icon"></i>
            <span id="device-type">检测中...</span>
          </div>
          <div class="network-info">
            <i class="fas fa-wifi" id="network-icon"></i>
            <span id="network-status">检测中...</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 扫描线效果 -->
    <div class="scanline"></div>

    <main class="main-container">
      <div class="dashboard-grid">
        <!-- 控制区域 -->
        <div class="control-section">
          <div class="control-panel panel">
        <h2 class="panel-title">
          <i class="fas fa-sliders-h"></i>
          机器人控制
        </h2>
        
        <div class="input-group">
          <label for="robot-ip">机器人IP地址</label>
          <input
            type="text"
            id="robot-ip"
            class="input-field"
            placeholder="例如: ***********"
          />
        </div>
        
        <div class="input-group">
          <label for="robot-token">安全Token</label>
          <input
            type="text"
            id="robot-token"
            class="input-field"
            placeholder="输入机器人安全Token"
          />
        </div>
        
        <div class="input-group">
          <label for="command">
            <i class="fas fa-terminal"></i>
            快捷命令
          </label>
          <select id="command" class="input-field">
            <option value="">选择命令...</option>
          </select>
        </div>
        
        <div class="input-group">
          <label for="gamepad">
            <i class="fas fa-gamepad"></i>
            控制器 (按住LB键控制)
          </label>
          <select id="gamepad" class="input-field">
            <option value="">未检测到手柄</option>
          </select>
        </div>
        
        <div class="button-group">
          <button id="connect-btn" class="btn btn-primary">
            <i class="fas fa-plug"></i>
            连接
          </button>
          <button id="execute-btn" class="btn btn-secondary">
            <i class="fas fa-play"></i>
            执行
          </button>
            </div>
        </div>
      </div>

        <!-- 视频区域 -->
        <div class="video-section">
          <div class="video-panel panel">
        <div class="panel-title">
          <i class="fas fa-video"></i> 实时视频流
        </div>
        <div class="video-container">
          <video id="video-frame" autoplay playsinline muted></video>
          <div id="video-placeholder" class="video-placeholder">
            <i class="fas fa-video-slash"></i>
            <div>无视频信号</div>
            <div>连接机器人以开始视频流</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 音频区域 -->
        <div class="audio-section">
        <!-- 语音发送功能 -->
          <div class="audio-panel panel">
          <div class="panel-title">
            <i class="fas fa-microphone"></i> 语音发送
          </div>
          
          <!-- 录音功能区域 -->
          <div class="voice-recording-section">
            <div class="recording-buttons">
              <button id="mic-record-btn" class="record-btn">
                <i class="fas fa-microphone"></i>
                <span>开始录音</span>
              </button>
              <button id="mic-stop-btn" class="record-btn stop-btn" style="display: none;">
                <i class="fas fa-stop"></i>
                <span>停止录音</span>
              </button>
            </div>
            
            <!-- 播放音量控制 -->
            <div class="audio-control-options">
              <div class="volume-control">
                <label for="robot-playback-volume">播放音量:</label>
                <input type="range" id="robot-playback-volume" min="50" max="500" value="200" class="volume-slider">
                <span id="robot-playback-volume-text">200%</span>
              </div>
            </div>
            
            <div class="recording-tips">
              <p><i class="fas fa-info-circle"></i> 点击录音按钮，说话后点击停止，机器狗会播放您的声音</p>
              <p><i class="fas fa-volume-up"></i> 播放音量：调整机器狗播放时的音量大小</p>
              <p><i class="fas fa-clock"></i> 最长录音时间：10秒</p>
              <div class="tips-buttons">
                <button id="test-audio-connection-btn" class="test-connection-btn">
                  <i class="fas fa-wifi"></i> 测试音频连接
                </button>
                <button id="test-mic-volume-btn" class="test-connection-btn">
                  <i class="fas fa-microphone"></i> 测试录音音量
                </button>
              </div>
              
              <!-- 音量预设 -->
              <div class="volume-presets" style="margin-top: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-size: 0.85rem; color: var(--text-secondary);">
                  <i class="fas fa-magic"></i> 音量预设：
                </label>
                <div class="tips-buttons">
                  <button id="volume-preset-quiet" class="test-connection-btn">
                    <i class="fas fa-volume-down"></i> 安静模式
                  </button>
                  <button id="volume-preset-normal" class="test-connection-btn">
                    <i class="fas fa-volume-up"></i> 正常模式
                  </button>
                  <button id="volume-preset-loud" class="test-connection-btn">
                    <i class="fas fa-bullhorn"></i> 大声模式
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 麦克风状态 -->
          <div id="mic-status" class="mic-status" style="display: none;">
            <i class="fas fa-microphone"></i>
            <span id="mic-status-text">准备录音</span>
            <div class="mic-level-container">
              <span>音量:</span>
              <div class="mic-level-bar">
                <div id="mic-level-fill" class="mic-level-fill"></div>
              </div>
              <span id="mic-level-text">0%</span>
            </div>
          </div>
          
          <!-- 录音发送日志 -->
          <div class="audio-log">
            <div id="audio-log-title">录音发送日志</div>
            <div id="audio-log-content">等待录音操作...</div>
          </div>
        </div>

        <!-- 机器狗音频播放功能 -->
          <div class="audio-panel panel">
          <div class="panel-title">
            <i class="fas fa-volume-up"></i> 机器狗音频播放
          </div>
          
          <!-- 音频播放控制区域 -->
          <div class="robot-audio-section">
            <div class="audio-control-buttons">
              <button id="robot-audio-start-btn" class="audio-control-btn start-btn">
                <i class="fas fa-play"></i>
                <span>开始播放</span>
              </button>
              <button id="robot-audio-stop-btn" class="audio-control-btn stop-btn" style="display: none;">
                <i class="fas fa-stop"></i>
                <span>停止播放</span>
              </button>
            </div>
            
            <!-- 音频播放状态 -->
            <div id="robot-audio-status" class="robot-audio-status" style="display: none;">
              <div class="audio-status-indicator">
                <i class="fas fa-headphones"></i>
                <span id="robot-audio-status-text">正在播放机器狗音频...</span>
              </div>
              <div class="audio-level-container">
                <span>音量:</span>
                <div class="audio-level-bar">
                  <div id="robot-audio-level-fill" class="audio-level-fill"></div>
                </div>
                <span id="robot-audio-level-text">0%</span>
              </div>
            </div>
            
            <!-- 音频控制选项 -->
            <div class="audio-control-options">
              <div class="volume-control">
                <label for="robot-audio-gain">音频增益:</label>
                <input type="range" id="robot-audio-gain" min="0" max="500" value="100" class="volume-slider">
                <span id="robot-audio-gain-text">100%</span>
              </div>
              
              <div class="audio-info">
                <p><i class="fas fa-info-circle"></i> 播放机器狗麦克风实时收音</p>
                <p><i class="fas fa-robot"></i> 可以听到机器狗周围环境的声音</p>
                <div class="info-buttons">
                  <button id="test-robot-audio-btn" class="test-connection-btn">
                    <i class="fas fa-headphones"></i> 测试音频接收
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 音频播放日志 -->
          <div class="audio-log">
            <div id="robot-audio-log-title">音频播放日志</div>
            <div id="robot-audio-log-content">等待音频播放操作...</div>
          </div>
        </div>
      </div>

        <!-- 命令和日志区域 -->
        <div class="command-section">
          <div class="custom-command panel">
      <h3 class="panel-title">
        <i class="fas fa-code"></i>
        自定义命令
      </h3>
      <div class="custom-command-input">
        <div class="input-group">
          <label for="custom-command">JSON 命令</label>
          <input
            type="text"
            id="custom-command"
            class="input-field"
            placeholder='输入自定义JSON命令，例如：{"type": "msg", "data": {...}}'
          />
        </div>
        <button id="execute-custom-btn" class="btn btn-secondary">
          <i class="fas fa-paper-plane"></i>
          发送
        </button>
      </div>
    </div>

          <div class="log-container panel">
      <h3 class="panel-title">
        <i class="fas fa-list-alt"></i>
        系统日志
      </h3>
      <div id="log" class="log">
        <code class="log-code" id="log-code">系统已初始化，准备连接...</code>
      </div>
    </div>
        </div>
      </div>
    </main>

    <!-- 操控模式选择按钮 -->
    <div id="control-mode-btn" class="control-mode-button">
      <i class="fas fa-cog"></i>
      <span>操控模式</span>
    </div>

    <!-- 操控模式选择弹窗 -->
    <div id="control-mode-modal" class="control-mode-modal" style="display: none;">
      <div class="modal-content">
        <h3>选择操控模式</h3>
        <div class="mode-options">
          <button id="desktop-mode-btn" class="mode-option">
            <i class="fas fa-keyboard"></i>
            <span>键盘操控</span>
            <small>使用WASD键控制移动和转向</small>
          </button>
          <button id="mobile-mode-btn" class="mode-option">
            <i class="fas fa-mobile-alt"></i>
            <span>手机操控</span>
            <small>使用虚拟摇杆控制</small>
          </button>
        </div>
        <button id="close-modal-btn" class="close-modal-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- 手机操控模式的全屏布局 -->
    <div id="mobile-control-layout" class="mobile-control-layout" style="display: none;">
      <div class="mobile-video-area">
        <video id="mobile-video-frame" autoplay playsinline muted webkit-playsinline controls="false"></video>
        <div class="mobile-video-placeholder">
          <div>
            <i class="fas fa-video-slash"></i>
            <p>无视频信号</p>
          </div>
        </div>
        <button id="exit-mobile-mode" class="exit-mobile-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="mobile-joystick-area">
        <!-- 速度控制面板 -->
        <div class="speed-control-panel">
          <div class="speed-control-header">
            <i class="fas fa-tachometer-alt"></i>
            <span>移动速度</span>
            <span id="speed-value">50%</span>
          </div>
          <input type="range" id="speed-slider" class="speed-slider" min="10" max="100" value="50" step="5">
          <div class="speed-labels">
            <span>慢</span>
            <span>快</span>
          </div>
        </div>
        
        <div id="mobile-joy-left" class="mobile-joystick left-joystick">
          <i class="fas fa-arrows-alt"></i>
        </div>
        <div id="mobile-joy-right" class="mobile-joystick right-joystick">
          <i class="fas fa-crosshairs"></i>
        </div>
      </div>
    </div>

    <!-- 原有的虚拟摇杆（隐藏） -->
    <div id="joy-left" class="corner-div bottom-left" style="display: none;">
      <i class="fas fa-arrows-alt" style="font-size: 1.5rem; color: var(--primary-color);"></i>
    </div>
    <div id="joy-right" class="corner-div bottom-right" style="display: none;">
      <i class="fas fa-crosshairs" style="font-size: 1.5rem; color: var(--accent-color);"></i>
    </div>

    <!-- 键盘控制悬浮窗 -->
    <div id="keyboard-control-window" class="keyboard-control-window" style="display: none;">
      <div class="keyboard-window-header">
        <div class="keyboard-window-title">
          <i class="fas fa-keyboard"></i>
          <span>键盘操控</span>
        </div>
        <div class="keyboard-window-controls">
          <button id="minimize-keyboard-btn" class="window-control-btn">
            <i class="fas fa-minus"></i>
          </button>
          <button id="close-keyboard-btn" class="window-control-btn close">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <div class="keyboard-window-content">
        <div class="keyboard-status">
          <div class="connection-status">
            <i class="fas fa-plug" id="keyboard-connection-icon"></i>
            <span id="keyboard-connection-text">未连接</span>
          </div>
          <div class="control-mode">
            <i class="fas fa-gamepad"></i>
            <span id="keyboard-mode-text">键盘控制已启用</span>
          </div>
        </div>
        
        <div class="keyboard-controls">
                                <div class="control-instructions">
             <h4>键盘控制说明：</h4>
             <p style="font-size: 0.8rem; color: var(--text-secondary); margin-bottom: 1rem; line-height: 1.4;">
               🎮 移动: W前进 S后退 | 🔄 转向: A左走 D右走 | ↔️ 平移: Q左转 E右转
             </p>
             <div class="key-mappings">
               <div class="key-mapping">
                 <kbd>W</kbd>
                 <span>前进</span>
                 <div class="key-status" id="key-w-status"></div>
               </div>
               <div class="key-mapping">
                 <kbd>S</kbd>
                 <span>后退</span>
                 <div class="key-status" id="key-s-status"></div>
               </div>
               <div class="key-mapping">
                 <kbd>A</kbd>
                 <span>左走</span>
                 <div class="key-status" id="key-a-status"></div>
               </div>
               <div class="key-mapping">
                 <kbd>D</kbd>
                 <span>右走</span>
                 <div class="key-status" id="key-d-status"></div>
               </div>
               <div class="key-mapping">
                 <kbd>Q</kbd>
                 <span>左转</span>
                 <div class="key-status" id="key-q-status"></div>
               </div>
               <div class="key-mapping">
                 <kbd>E</kbd>
                 <span>右转</span>
                 <div class="key-status" id="key-e-status"></div>
               </div>
             </div>
          </div>
          
          <div class="control-settings">
            <div class="speed-control">
              <label>移动速度:</label>
              <input type="range" id="keyboard-speed" min="0.1" max="1.0" value="0.5" step="0.1">
              <span id="keyboard-speed-text">50%</span>
            </div>
            <div class="turn-control">
              <label>转向速度:</label>
              <input type="range" id="keyboard-turn-speed" min="0.1" max="1.0" value="0.3" step="0.1">
              <span id="keyboard-turn-speed-text">30%</span>
            </div>
          </div>
          
          <div class="control-feedback">
            <div class="current-command">
              <span>当前指令:</span>
              <code id="current-command-display">无</code>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 键盘控制最小化状态 -->
    <div id="keyboard-control-minimized" class="keyboard-control-minimized" style="display: none;">
      <div class="minimized-content" id="restore-keyboard-btn">
        <i class="fas fa-keyboard"></i>
        <span>键盘操控</span>
        <div class="active-keys" id="active-keys-display"></div>
      </div>
    </div>

    <!-- 麦克风录音功能 -->
    <script>
      class MicrophoneRecorder {
        constructor() {
          this.mediaRecorder = null;
          this.audioChunks = [];
          this.stream = null;
          this.audioContext = null;
          this.analyser = null;
          this.dataArray = null;
          this.isRecording = false;
          this.levelUpdateInterval = null;
          this.maxRecordTime = 10000; // 最长录音10秒
          this.recordStartTime = 0;
          this.micGainNode = null;

          // 音量控制设置
          this.currentPlaybackVolume = 2.0; // 默认200%播放音量
          
          // 绑定界面元素
          this.recordBtn = document.getElementById('mic-record-btn');
          this.stopBtn = document.getElementById('mic-stop-btn');
          this.micStatus = document.getElementById('mic-status');
          this.micStatusText = document.getElementById('mic-status-text');
          this.micLevelFill = document.getElementById('mic-level-fill');
          this.micLevelText = document.getElementById('mic-level-text');
          this.audioLogContent = document.getElementById('audio-log-content');
          
          // 音量控制元素
          this.playbackVolumeSlider = document.getElementById('robot-playback-volume');
          this.playbackVolumeText = document.getElementById('robot-playback-volume-text');
          
          this.initEventListeners();
        }
        
        initEventListeners() {
          this.recordBtn.addEventListener('click', () => this.startRecording());
          this.stopBtn.addEventListener('click', () => this.stopRecording());
          
          // 测试音频连接按钮
          const testBtn = document.getElementById('test-audio-connection-btn');
          if (testBtn) {
            testBtn.addEventListener('click', () => this.testAudioConnection());
          }
          
          // 测试录音音量按钮
          const testMicBtn = document.getElementById('test-mic-volume-btn');
          if (testMicBtn) {
            testMicBtn.addEventListener('click', () => this.testMicrophoneVolume());
          }
          
          // 播放音量控制
          if (this.playbackVolumeSlider) {
            this.playbackVolumeSlider.addEventListener('input', (e) => this.updatePlaybackVolume(e.target.value));
          }
          
          // 音量预设按钮
          const presetQuiet = document.getElementById('volume-preset-quiet');
          const presetNormal = document.getElementById('volume-preset-normal');
          const presetLoud = document.getElementById('volume-preset-loud');
          
          if (presetQuiet) {
            presetQuiet.addEventListener('click', () => this.setVolumePreset('quiet'));
          }
          if (presetNormal) {
            presetNormal.addEventListener('click', () => this.setVolumePreset('normal'));
          }
          if (presetLoud) {
            presetLoud.addEventListener('click', () => this.setVolumePreset('loud'));
          }
        }
        
        // 更新机器狗播放音量（这个设置会在发送音频时使用）
        updatePlaybackVolume(value) {
          this.currentPlaybackVolume = Math.min(value / 100, 5.0); // 最大500%
          
          if (this.playbackVolumeText) {
            this.playbackVolumeText.textContent = `${value}%`;
          }
          
          this.log(`🔊 播放音量调节至: ${value}% (倍数: ${this.currentPlaybackVolume.toFixed(1)}x)`);
        }
        
        // 测试麦克风音量功能
        async testMicrophoneVolume() {
          this.log('🧪 开始测试麦克风音量...');
          
                   // 移动端网络状态检查（异步，不阻塞测试）
         if (window.deviceManager && window.deviceManager.isMobile && window.networkManager) {
           window.networkManager.testNetworkLatency().then(networkTest => {
             this.log(`🌐 网络状态: ${networkTest.quality} (延迟: ${networkTest.latency}ms)`);
             if (networkTest.quality === 'poor') {
               this.log('⚠️ 网络较差可能影响音频传输质量');
             }
           }).catch(() => {
             this.log('🌐 网络状态检查跳过');
           });
         }
          
          try {
            // 临时申请麦克风权限进行测试
            this.log('🎤 申请麦克风权限进行音量测试...');
            const testStream = await navigator.mediaDevices.getUserMedia({ 
              audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: false,
                noiseSuppression: false,
                autoGainControl: false
              } 
            });
            
            // 创建音频分析器
            const testAudioContext = new (window.AudioContext || window.webkitAudioContext)();
            const testAnalyser = testAudioContext.createAnalyser();
            const testGainNode = testAudioContext.createGain();
            
            // 应用固定增益设置
            testGainNode.gain.value = 3.0; // 300%
            
            const source = testAudioContext.createMediaStreamSource(testStream);
            source.connect(testGainNode);
            testGainNode.connect(testAnalyser);
            
            testAnalyser.fftSize = 256;
            const bufferLength = testAnalyser.frequencyBinCount;
            const testDataArray = new Uint8Array(bufferLength);
            
            this.log(`✅ 音量测试开始，当前增益: 300%`);
            this.log('🎤 请对着麦克风说话，测试持续5秒...');
            
            let maxVolume = 0;
            let avgVolume = 0;
            let sampleCount = 0;
            
            // 进行5秒测试
            const testDuration = 5000;
            const testStartTime = Date.now();
            
            const volumeTestInterval = setInterval(() => {
              testAnalyser.getByteFrequencyData(testDataArray);
              
              let sum = 0;
              for (let i = 0; i < testDataArray.length; i++) {
                sum += testDataArray[i];
              }
              const currentVolume = sum / testDataArray.length;
              const percentage = Math.round((currentVolume / 255) * 100);
              
              maxVolume = Math.max(maxVolume, percentage);
              avgVolume = (avgVolume * sampleCount + percentage) / (sampleCount + 1);
              sampleCount++;
              
              // 实时显示音量
              if (sampleCount % 10 === 0) { // 每秒显示一次
                this.log(`📊 当前音量: ${percentage}%, 最大: ${maxVolume}%, 平均: ${avgVolume.toFixed(0)}%`);
              }
              
              if (Date.now() - testStartTime >= testDuration) {
                clearInterval(volumeTestInterval);
                
                // 显示测试结果
                this.log(`🎉 音量测试完成！`);
                this.log(`📊 测试结果 - 最大音量: ${maxVolume}%, 平均音量: ${avgVolume.toFixed(0)}%`);
                
                if (maxVolume < 20) {
                  this.log(`⚠️ 音量偏低，建议增加录音音量至400%以上`);
                } else if (maxVolume > 80) {
                  this.log(`⚠️ 音量偏高，建议降低录音音量避免失真`);
                } else {
                  this.log(`✅ 音量合适，当前设置良好`);
                }
                
                // 清理资源
                testStream.getTracks().forEach(track => track.stop());
                testAudioContext.close();
              }
            }, 100);
            
          } catch (error) {
            this.log(`❌ 麦克风音量测试失败: ${error.message}`);
            if (error.name === 'NotAllowedError') {
              this.log('💡 请授权麦克风权限后重试');
            }
            console.error('音量测试失败:', error);
          }
        }
        
        // 设置音量预设
        setVolumePreset(preset) {
          let playbackVolume;
          
          switch(preset) {
            case 'quiet':
              playbackVolume = 150; // 150%
              this.log('🔇 切换到安静模式：播放150%');
              break;
            case 'normal':
              playbackVolume = 200; // 200%
              this.log('🔊 切换到正常模式：播放200%');
              break;
            case 'loud':
              playbackVolume = 300; // 300%
              this.log('📢 切换到大声模式：播放300%');
              break;
            default:
              return;
          }
          
          // 更新滑块和内部值
          if (this.playbackVolumeSlider) {
            this.playbackVolumeSlider.value = playbackVolume;
            this.updatePlaybackVolume(playbackVolume);
          }
        }
        
        // 测试音频连接功能
        async testAudioConnection() {
          this.log('🧪 开始测试音频连接...');
          
          // 使用新的连接检查方法
          const connectionCheck = this.checkConnectionStatus();
          if (!connectionCheck.isReady) {
            this.log(`❌ 测试失败: ${connectionCheck.reason}`);
            this.debugConnectionState();
            return;
          }
          
          // 尝试发送一个简单的测试命令
          try {
            this.log('📡 发送测试音频开始命令...');
            await this.sendCommand(4001, "", "测试开始信号");
            await this.delay(1000);
            
            this.log('📡 发送测试音频结束命令...');
            await this.sendCommand(4002, "", "测试结束信号");
            
            this.log('✅ 音频连接测试成功！可以正常录音发送');
          } catch (error) {
            this.log(`❌ 音频连接测试失败: ${error.message}`);
            console.error('音频连接测试错误:', error);
            
            // 输出详细的调试信息
            this.debugConnectionState();
          }
        }
        

        
        async startRecording() {
          try {
            // 使用强化的连接状态检查
            const connectionCheck = this.checkConnectionStatus();
            if (!connectionCheck.isReady) {
              this.log(`❌ 无法开始录音: ${connectionCheck.reason}`);
              this.debugConnectionState();
              return;
            }
            
            this.log('✅ 连接状态检查通过，准备开始录音');
            this.log('🎤 请求麦克风权限...');
            
            // 使用优化的移动端音频管理器
            if (window.mobileAudioManager && window.deviceManager.isMobile) {
              // 移动端优化路径
              this.log('📱 检测到移动设备，使用移动端优化...');
              
              // 等待移动端音频就绪
              if (!window.mobileAudioManager.isAudioEnabled) {
                this.log('⏳ 等待音频上下文启用，请点击屏幕任意位置...');
                await new Promise((resolve) => {
                  const enableAudio = () => {
                    window.removeEventListener('mobile-audio-ready', enableAudio);
                    resolve();
                  };
                  window.addEventListener('mobile-audio-ready', enableAudio);
                  
                  // 5秒超时
                  setTimeout(() => {
                    window.removeEventListener('mobile-audio-ready', enableAudio);
                    resolve();
                  }, 5000);
                });
              }
              
              this.stream = await window.mobileAudioManager.requestMicrophonePermission();
            } else {
              // 桌面端路径
            this.stream = await navigator.mediaDevices.getUserMedia({ 
              audio: {
                sampleRate: 16000,  // 16kHz采样率适合语音和机器狗
                channelCount: 1,    // 单声道减少数据量
                echoCancellation: false, // 关闭回声消除避免音量衰减
                noiseSuppression: false, // 关闭噪音抑制保持音量
                autoGainControl: false,  // 关闭自动增益控制
                volume: 1.0             // 设置最大音量
              } 
            });
            }
            
            // 创建音频上下文用于实时音量检测和增益控制
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
              sampleRate: 16000
            });
            this.analyser = this.audioContext.createAnalyser();
            this.micGainNode = this.audioContext.createGain();
            
            // 设置麦克风增益（固定值）
            this.micGainNode.gain.value = 3.0; // 300%增益
            
            const source = this.audioContext.createMediaStreamSource(this.stream);
            source.connect(this.micGainNode);
            this.micGainNode.connect(this.analyser);
            
            this.analyser.fftSize = 256;
            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);
            
            // 设置录音器，优先使用易转换的格式
            const options = {
              audioBitsPerSecond: 32000  // 32kbps比特率，平衡质量和大小
            };
            
            // 按优先级选择音频格式（优先选择易转换的格式）
            if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
              options.mimeType = 'audio/webm;codecs=opus';
              this.log('✅ 使用WebM/Opus格式录音');
            } else if (MediaRecorder.isTypeSupported('audio/webm')) {
              options.mimeType = 'audio/webm';
              this.log('✅ 使用WebM格式录音');
            } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
              options.mimeType = 'audio/mp4';
              this.log('✅ 使用MP4格式录音');
            } else {
              this.log('⚠️ 使用浏览器默认录音格式');
            }
            
            this.mediaRecorder = new MediaRecorder(this.stream, options);
            this.audioChunks = [];
            this.recordStartTime = Date.now();
            
            this.mediaRecorder.ondataavailable = (event) => {
              if (event.data.size > 0) {
                this.audioChunks.push(event.data);
              }
            };
            
            this.mediaRecorder.onstop = () => {
              this.processRecording();
            };
            
            this.mediaRecorder.onerror = (event) => {
              this.log(`❌ 录音错误: ${event.error}`);
              this.stopRecording();
            };
            
            // 开始录音
            this.mediaRecorder.start(1000); // 每秒收集一次数据
            this.isRecording = true;
            
            // 更新UI
            this.updateRecordingUI(true);
            this.startLevelMonitoring();
            
            // 检测是否有机器狗音频播放，如果有则增强录音增益
            const isRobotAudioPlaying = window.robotAudioPlayer && window.robotAudioPlayer.isPlaying;
            if (isRobotAudioPlaying) {
              // 播放时增强增益
              this.micGainNode.gain.value = 5.0;
              this.log('🔊 检测到机器狗音频播放，自动增强录音增益至500%');
            } else {
              this.micGainNode.gain.value = 3.0;
              this.log('🎤 使用正常录音增益300%');
            }
            
            this.log('✅ 开始录音，最长10秒，请开始说话...');
            
            // 设置最大录音时间限制
            this.maxRecordingTimer = setTimeout(() => {
              if (this.isRecording) {
                this.log('⏰ 达到最大录音时间，自动停止');
                this.stopRecording();
              }
            }, this.maxRecordTime);
            
          } catch (error) {
            this.log(`❌ 录音启动失败: ${error.message}`);
            if (error.name === 'NotAllowedError') {
              this.log('💡 请授权麦克风权限后重试');
            }
            console.error('录音失败:', error);
          }
        }
        
        stopRecording() {
          if (!this.isRecording) {
            return;
          }
          
          try {
            // 清除定时器
            if (this.maxRecordingTimer) {
              clearTimeout(this.maxRecordingTimer);
              this.maxRecordingTimer = null;
            }
            
            // 停止录音
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
              this.mediaRecorder.stop();
            }
            
            this.isRecording = false;
            
            // 停止流
            if (this.stream) {
              this.stream.getTracks().forEach(track => track.stop());
              this.stream = null;
            }
            
            // 停止音频上下文
            if (this.audioContext) {
              this.audioContext.close();
              this.audioContext = null;
            }
            
            // 清理麦克风增益节点
            this.micGainNode = null;
            
            // 停止音量监控
            this.stopLevelMonitoring();
            
            // 更新UI
            this.updateRecordingUI(false);
            
            const recordDuration = (Date.now() - this.recordStartTime) / 1000;
            this.lastRecordingDuration = recordDuration; // 保存录音时长
            this.log(`⏹️ 录音已停止，时长: ${recordDuration.toFixed(1)}秒`);
            
          } catch (error) {
            this.log(`❌ 停止录音时出错: ${error.message}`);
            console.error('停止录音错误:', error);
          }
        }
        
        async processRecording() {
          try {
            if (this.audioChunks.length === 0) {
              this.log('❌ 没有录音数据');
              return;
            }
            
            const totalProcessStart = Date.now();
            
            // 合并音频块
            const audioBlob = new Blob(this.audioChunks, { type: this.mediaRecorder.mimeType });
            const audioSize = (audioBlob.size / 1024).toFixed(1);
            
            this.log(`📁 录音完成，原始大小: ${audioSize}KB，开始快速处理...`);
            
            // 快速转换为WAV格式
            const conversionStart = Date.now();
            const wavBlob = await this.convertToWav(audioBlob);
            const conversionTime = (Date.now() - conversionStart) / 1000;
            
            if (wavBlob) {
              const wavSize = (wavBlob.size / 1024).toFixed(1);
              this.log(`🔄 音频格式转换完成: ${wavSize}KB WAV格式, 耗时: ${conversionTime.toFixed(1)}秒`);
              
              // 发送WAV格式给机器狗
              await this.sendToRobot(wavBlob, totalProcessStart);
            } else {
              this.log('❌ 音频转换失败');
            }
            
          } catch (error) {
            this.log(`❌ 处理录音失败: ${error.message}`);
            console.error('处理录音失败:', error);
          } finally {
            // 清理资源
            this.audioChunks = [];
          }
        }
        
        async convertToWav(audioBlob) {
          try {
            this.log('🔄 开始转换为WAV格式...');
            
            // 使用更安全的方法转换为WAV格式
            const arrayBuffer = await audioBlob.arrayBuffer();
            this.log(`📁 原始音频大小: ${(arrayBuffer.byteLength/1024).toFixed(1)}KB`);
            
            // 使用Web Audio API解码音频
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            let audioBuffer;
            try {
              audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice(0));
              this.log(`🎵 音频解码成功: ${audioBuffer.duration.toFixed(2)}秒, ${audioBuffer.sampleRate}Hz, ${audioBuffer.numberOfChannels}声道`);
            } catch (decodeError) {
              this.log(`❌ 音频解码失败: ${decodeError.message}`);
              this.log('🔄 尝试发送原始格式...');
              return audioBlob;
            }
            
            // 转换为WAV格式
            const wavBuffer = await this.createWavBuffer(audioBuffer);
            const wavBlob = new Blob([wavBuffer], { type: 'audio/wav' });
            
            this.log(`✅ WAV转换完成: ${(wavBlob.size/1024).toFixed(1)}KB`);
            
            // 关闭音频上下文释放资源
            audioContext.close();
            
            return wavBlob;
            
          } catch (error) {
            this.log(`❌ WAV转换失败: ${error.message}，使用原始格式`);
            return audioBlob;
          }
        }
        
        // 安全的WAV格式创建方法
        async createWavBuffer(audioBuffer) {
          const sampleRate = audioBuffer.sampleRate;
          const numChannels = audioBuffer.numberOfChannels;
          const length = audioBuffer.length;
          
                      this.log(`🔧 创建WAV: ${sampleRate}Hz, ${numChannels}声道, ${length}样本, 播放音量${(this.currentPlaybackVolume * 100).toFixed(0)}%`);
          
          // 准备音频数据 - 转换为16位PCM
          const audioData = new Int16Array(length * numChannels);
          
          // 分批处理音频数据避免阻塞UI
          const batchSize = 8192; // 8K样本为一批
          let offset = 0;
          
          for (let i = 0; i < length; i += batchSize) {
            const end = Math.min(i + batchSize, length);
            
            for (let sample = i; sample < end; sample++) {
              for (let channel = 0; channel < numChannels; channel++) {
                const channelData = audioBuffer.getChannelData(channel);
                let pcmSample = channelData[sample];
                
                // 应用播放音量增益
                pcmSample = pcmSample * this.currentPlaybackVolume;
                
                // 限制范围并转换为16位整数
                pcmSample = Math.max(-1, Math.min(1, pcmSample));
                audioData[offset++] = pcmSample < 0 ? pcmSample * 0x8000 : pcmSample * 0x7FFF;
              }
            }
            
            // 每处理一批让出CPU时间
            if (i % (batchSize * 10) === 0 && i > 0) {
              this.log(`📊 WAV数据处理进度: ${((i / length) * 100).toFixed(1)}%`);
              await new Promise(resolve => setTimeout(resolve, 1));
            }
          }
          
          // 创建WAV文件头
          const header = this.createWavHeader(audioData.length * 2, sampleRate, numChannels);
          
          // 合并头部和数据
          const wavBuffer = new ArrayBuffer(header.byteLength + audioData.byteLength);
          const view = new Uint8Array(wavBuffer);
          
          view.set(new Uint8Array(header), 0);
          view.set(new Uint8Array(audioData.buffer), header.byteLength);
          
          this.log(`✅ WAV文件创建完成: ${(wavBuffer.byteLength/1024).toFixed(1)}KB`);
          return wavBuffer;
        }
        
        // 创建WAV文件头
        createWavHeader(dataLength, sampleRate, numChannels) {
          const header = new ArrayBuffer(44);
          const view = new DataView(header);
          
          // WAV文件头
          const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
              view.setUint8(offset + i, string.charCodeAt(i));
            }
          };
          
          writeString(0, 'RIFF');
          view.setUint32(4, 36 + dataLength, true);
          writeString(8, 'WAVE');
          writeString(12, 'fmt ');
          view.setUint32(16, 16, true);
          view.setUint16(20, 1, true);
          view.setUint16(22, numChannels, true);
          view.setUint32(24, sampleRate, true);
          view.setUint32(28, sampleRate * numChannels * 2, true);
          view.setUint16(32, numChannels * 2, true);
          view.setUint16(34, 16, true);
          writeString(36, 'data');
          view.setUint32(40, dataLength, true);
          
          return header;
        }
        

        
        async sendToRobot(audioBlob, totalProcessStart = null) {
          try {
            // 强化连接状态检查
            const connectionCheck = this.checkConnectionStatus();
            if (!connectionCheck.isReady) {
              this.log(`❌ ${connectionCheck.reason}`);
              return;
            }
            
            this.log('📡 开始发送音频到机器狗...');
            
            // 安全的base64转换 - 分片处理避免栈溢出
            const arrayBuffer = await audioBlob.arrayBuffer();
            const uint8Array = new Uint8Array(arrayBuffer);
            
            this.log(`🔢 开始base64编码，数据大小: ${uint8Array.length} 字节`);
            const base64String = await this.arrayBufferToBase64(uint8Array);
            this.log(`✅ base64编码完成，长度: ${base64String.length}`);
            
            
                                                                               // 统一使用63KB分块，最大化传输效率
                const audioSizeKB = arrayBuffer.byteLength / 1024;
                const chunkSize = 63 * 1024; // 63KB统一分块
                const delayBetweenChunks = 45; // 平衡的延迟设置
                
                this.log(`🚀 高效传输模式: ${chunkSize/1024}KB分块, ${delayBetweenChunks}ms间隔`);
            
            const chunks = [];
            for (let i = 0; i < base64String.length; i += chunkSize) {
              chunks.push(base64String.slice(i, i + chunkSize));
            }
            
            // 计算播放时长 - 使用实际录音时长
            const actualRecordingDuration = this.lastRecordingDuration || 1; // 使用实际录音时长
            this.log(`📦 音频: ${(arrayBuffer.byteLength/1024).toFixed(1)}KB, ${chunks.length}包, 实际录音${actualRecordingDuration.toFixed(1)}秒`);
            this.log(`🎛️ 音量设置: 播放${(this.currentPlaybackVolume * 100).toFixed(0)}%`);
            
            // 发送开始信号 - 增加错误处理
            try {
              await this.sendCommand(4001, "", "开始信号");
              await this.delay(500); // 增加等待时间确保信号处理
            } catch (error) {
              this.log(`❌ 发送开始信号失败: ${error.message}`);
              return;
            }
            
            // 发送数据包 - 增加连接状态检查
            this.log(`🚀 开始传输${chunks.length}个数据包...`);
            const startTime = Date.now();
            let successCount = 0;
            
            for (let i = 0; i < chunks.length; i++) {
              // 每次发送前检查连接状态
              const connectionCheck = this.checkConnectionStatus();
              if (!connectionCheck.isReady) {
                this.log(`❌ 传输中断: ${connectionCheck.reason}`);
                return;
              }
              
              const audioBlock = {
                current_block_index: i + 1,
                total_block_number: chunks.length,
                block_content: chunks[i]
              };
              
              try {
                await this.sendCommand(4003, JSON.stringify(audioBlock), `数据包 ${i + 1}/${chunks.length}`);
                successCount++;
                this.log(`📤 ${i + 1}/${chunks.length} (${(chunks[i].length/1024).toFixed(1)}KB) ✅`);
              } catch (error) {
                this.log(`❌ 数据包 ${i + 1} 发送失败: ${error.message}`);
                // 尝试重发一次
                try {
                  await this.delay(200);
                  await this.sendCommand(4003, JSON.stringify(audioBlock), `重发数据包 ${i + 1}`);
                  successCount++;
                  this.log(`📤 ${i + 1}/${chunks.length} (重发成功) ✅`);
                } catch (retryError) {
                  this.log(`❌ 数据包 ${i + 1} 重发也失败，跳过`);
                }
              }
              
              // 使用固定延迟确保稳定性
              if (i < chunks.length - 1) {
                await this.delay(delayBetweenChunks);
              }
            }
            
            const transmissionTime = (Date.now() - startTime) / 1000;
            const transmissionSpeed = (audioSizeKB / transmissionTime).toFixed(1);
            this.log(`📊 传输完成: ${successCount}/${chunks.length}包成功, 耗时${transmissionTime.toFixed(1)}秒, 速度${transmissionSpeed}KB/s`);
            
            if (successCount < chunks.length) {
              this.log(`⚠️ 有 ${chunks.length - successCount} 个数据包传输失败，音频可能不完整`);
            }
            
            // 计算等待时间 - 基于实际录音时长
            const bufferTime = 1.0; // 适度的缓冲时间
            const remainingWaitTime = Math.max(actualRecordingDuration + bufferTime, 1); // 不减去传输时间，因为播放和传输可能并行
            
            this.log(`⏰ 等待${remainingWaitTime.toFixed(1)}秒让机器狗完整播放（录音${actualRecordingDuration.toFixed(1)}s + 缓冲${bufferTime}s）...`);
            await this.delay(remainingWaitTime * 1000);
            
            // 发送结束信号 - 增加错误处理
            try {
              await this.sendCommand(4002, "", "结束信号");
              this.log('✅ 播放完成，发送结束信号');
              this.log('🎉 音频播放完成！');
              
              // 成功提示音
              this.playSuccessSound();
              
              // 显示总处理时间
              if (totalProcessStart) {
                const totalTime = (Date.now() - totalProcessStart) / 1000;
                this.log(`🎉 全部完成！总耗时: ${totalTime.toFixed(1)}秒 (转换+传输+播放)`);
              }
            } catch (error) {
              this.log(`❌ 发送结束信号失败: ${error.message}`);
            }
            
          } catch (error) {
            this.log(`❌ 发送音频失败: ${error.message}`);
            console.error('发送音频失败:', error);
          }
        }
        
        // 新增：检查连接状态的方法
        checkConnectionStatus() {
          if (!globalThis.rtc) {
            return { isReady: false, reason: 'WebRTC未初始化，请先连接到机器狗' };
          }
          
          if (!globalThis.rtc.channel) {
            return { isReady: false, reason: '数据通道未创建' };
          }
          
          if (globalThis.rtc.channel.readyState !== 'open') {
            return { isReady: false, reason: `数据通道状态异常: ${globalThis.rtc.channel.readyState}` };
          }
          
          if (globalThis.rtc.validationResult !== 'SUCCESS') {
            return { isReady: false, reason: `WebRTC验证未完成: ${globalThis.rtc.validationResult}` };
          }
          
          if (!globalThis.rtc.connected) {
            return { isReady: false, reason: 'WebRTC连接状态为断开' };
          }
          
          return { isReady: true, reason: '连接正常' };
        }
        
        // 新增：发送命令的方法，包含错误处理
        async sendCommand(apiId, data, description) {
          return new Promise((resolve, reject) => {
            try {
              // 再次检查连接状态
              const connectionCheck = this.checkConnectionStatus();
              if (!connectionCheck.isReady) {
                reject(new Error(connectionCheck.reason));
                return;
              }
              
              // 发送命令
              globalThis.rtc.publishApi("rt/api/audiohub/request", apiId, data);
              
              // 简单的成功确认（因为publishApi不返回Promise）
              setTimeout(() => {
                resolve();
              }, 50);
              
            } catch (error) {
              reject(error);
            }
          });
        }
        
        // 安全的base64转换方法 - 避免栈溢出
        async arrayBufferToBase64(uint8Array) {
          try {
            let binary = '';
            const chunkSize = 1024; // 1KB 分片处理
            
            this.log(`🔢 分片处理base64编码，总大小: ${uint8Array.length}字节，分片大小: ${chunkSize}`);
            
            for (let i = 0; i < uint8Array.length; i += chunkSize) {
              const chunk = uint8Array.slice(i, i + chunkSize);
              
              // 使用循环替代String.fromCharCode.apply避免栈溢出
              for (let j = 0; j < chunk.length; j++) {
                binary += String.fromCharCode(chunk[j]);
              }
              
              // 每处理10KB显示进度，并允许浏览器处理其他任务
              if (i % (10 * 1024) === 0 && i > 0) {
                this.log(`📊 base64编码进度: ${((i / uint8Array.length) * 100).toFixed(1)}%`);
                await new Promise(resolve => setTimeout(resolve, 1)); // 让出CPU时间
              }
            }
            
            this.log('🔄 正在生成最终base64字符串...');
            const base64 = btoa(binary);
            this.log(`✅ base64编码完成，输出长度: ${base64.length}`);
            return base64;
            
          } catch (error) {
            this.log(`❌ base64编码失败: ${error.message}`);
            
            // 如果分片处理也失败，尝试使用FileReader（更安全）
            this.log('🔄 尝试使用FileReader方法...');
            return await this.arrayBufferToBase64FileReader(uint8Array);
          }
        }
        
        // 备用的base64转换方法 - 使用FileReader
        arrayBufferToBase64FileReader(uint8Array) {
          return new Promise((resolve, reject) => {
            try {
              this.log('🔄 使用FileReader进行base64转换...');
              
              const blob = new Blob([uint8Array]);
              const reader = new FileReader();
              
              reader.onload = () => {
                const base64 = reader.result.split(',')[1]; // 移除data:前缀
                this.log(`✅ FileReader base64转换完成，长度: ${base64.length}`);
                resolve(base64);
              };
              
              reader.onerror = () => {
                this.log('❌ FileReader base64转换失败');
                reject(new Error('FileReader转换失败'));
              };
              
              reader.readAsDataURL(blob);
              
            } catch (error) {
              this.log(`❌ FileReader转换出错: ${error.message}`);
              reject(error);
            }
          });
        }
        
        // 延迟函数
        delay(ms) {
          return new Promise(resolve => setTimeout(resolve, ms));
        }
        

        
        // 播放成功提示音
        playSuccessSound() {
          try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
          } catch (error) {
            // 忽略提示音错误
          }
        }
        
        updateRecordingUI(recording) {
          try {
            if (recording) {
              if (this.recordBtn) {
                this.recordBtn.style.display = 'none';
                this.recordBtn.disabled = true;
              }
              if (this.stopBtn) {
                this.stopBtn.style.display = 'flex';
                this.stopBtn.disabled = false;
                const stopSpan = this.stopBtn.querySelector('span');
                if (stopSpan) stopSpan.textContent = '停止录音';
              }
              if (this.micStatus) {
                this.micStatus.style.display = 'flex';
              }
              if (this.micStatusText) {
                this.micStatusText.textContent = '🔴 正在录音...';
              }
            } else {
              if (this.recordBtn) {
                this.recordBtn.style.display = 'flex';
                this.recordBtn.disabled = false;
                const recordSpan = this.recordBtn.querySelector('span');
                if (recordSpan) recordSpan.textContent = '开始录音';
              }
              if (this.stopBtn) {
                this.stopBtn.style.display = 'none';
                this.stopBtn.disabled = true;
              }
              if (this.micStatus) {
                this.micStatus.style.display = 'none';
              }
              if (this.micLevelFill) {
                this.micLevelFill.style.width = '0%';
              }
              if (this.micLevelText) {
                this.micLevelText.textContent = '0%';
              }
            }
          } catch (error) {
            console.error('更新录音UI时出错:', error);
          }
        }
        
        startLevelMonitoring() {
          this.levelUpdateInterval = setInterval(() => {
            if (this.analyser && this.dataArray && this.isRecording) {
              this.analyser.getByteFrequencyData(this.dataArray);
              
              // 计算平均音量
              let sum = 0;
              for (let i = 0; i < this.dataArray.length; i++) {
                sum += this.dataArray[i];
              }
              const average = sum / this.dataArray.length;
              const percentage = Math.round((average / 255) * 100);
              
              // 更新UI
              this.micLevelFill.style.width = `${percentage}%`;
              this.micLevelText.textContent = `${percentage}%`;
              
              // 更新录音时间显示
              if (this.recordStartTime) {
                const elapsed = (Date.now() - this.recordStartTime) / 1000;
                const remaining = Math.max(0, (this.maxRecordTime / 1000) - elapsed);
                this.micStatusText.textContent = `🔴 录音中... (${remaining.toFixed(0)}s)`;
              }
            }
          }, 100);
        }
        
        stopLevelMonitoring() {
          if (this.levelUpdateInterval) {
            clearInterval(this.levelUpdateInterval);
            this.levelUpdateInterval = null;
          }
        }
        
        // 调试连接状态
        debugConnectionState() {
          const debugInfo = {
            hasRTC: !!globalThis.rtc,
            connected: globalThis.rtc?.connected || false,
            hasChannel: !!globalThis.rtc?.channel,
            channelState: globalThis.rtc?.channel?.readyState || 'undefined',
            validationResult: globalThis.rtc?.validationResult || 'undefined'
          };
          
          console.log('🔍 WebRTC连接调试信息:', debugInfo);
          this.log(`🔍 调试: RTC=${debugInfo.hasRTC}, 连接=${debugInfo.connected}, 通道=${debugInfo.channelState}, 验证=${debugInfo.validationResult}`);
          
          return debugInfo;
        }
        
        log(message) {
          const timestamp = new Date().toLocaleTimeString();
          this.audioLogContent.innerHTML += `<div style="margin: 2px 0; padding: 2px; border-left: 3px solid #00d4ff; padding-left: 8px;">[${timestamp}] ${message}</div>`;
          this.audioLogContent.scrollTop = this.audioLogContent.scrollHeight;
          
          // 也在控制台输出
          console.log(`[录音] ${message}`);
        }
      }

      // 机器狗音频播放功能类
      class RobotAudioPlayer {
        constructor() {
          this.audioElement = null;
          this.audioContext = null;
          this.analyser = null;
          this.dataArray = null;
          this.isPlaying = false;
          this.levelUpdateInterval = null;
          this.currentGain = 1.0; // 默认100%增益
          this.gainNode = null; // 音频增益节点
          
          // 绑定界面元素
          this.startBtn = document.getElementById('robot-audio-start-btn');
          this.stopBtn = document.getElementById('robot-audio-stop-btn');
          this.audioStatus = document.getElementById('robot-audio-status');
          this.statusText = document.getElementById('robot-audio-status-text');
          this.audioLevelFill = document.getElementById('robot-audio-level-fill');
          this.audioLevelText = document.getElementById('robot-audio-level-text');
          this.gainSlider = document.getElementById('robot-audio-gain');
          this.gainText = document.getElementById('robot-audio-gain-text');
          this.logContent = document.getElementById('robot-audio-log-content');
          
          this.initEventListeners();
        }
        
        initEventListeners() {
          // 播放控制按钮
          this.startBtn.addEventListener('click', () => this.startPlayback());
          this.stopBtn.addEventListener('click', () => this.stopPlayback());
          
          // 增益控制
          this.gainSlider.addEventListener('input', (e) => this.updateGain(e.target.value));
          
          // 测试音频接收按钮
          const testBtn = document.getElementById('test-robot-audio-btn');
          if (testBtn) {
            testBtn.addEventListener('click', () => this.testAudioConnection());
          }
          
          // 添加调试模式：每5秒检查一次音频状态
          this.debugInterval = setInterval(() => {
            this.debugAudioStatus();
          }, 5000);
        }
        
        // 测试音频接收功能
        async testAudioConnection() {
          this.log('🧪 开始测试音频接收...');
          
          // 检查WebRTC连接状态
          if (!globalThis.rtc) {
            this.log('❌ WebRTC未初始化，请先连接到机器狗');
            return;
          }
          
          if (!globalThis.rtc.connected) {
            this.log('❌ WebRTC未连接，请先建立连接');
            return;
          }
          
          // 尝试重新启用音频流
          this.log('🔄 尝试重新启用机器狗音频传输...');
          try {
            globalThis.rtc.publish("", "on", globalThis.DataChannelType.AUD);
            this.log('📡 已发送音频启用命令');
            
            // 等待一段时间让音频轨道建立
            await new Promise(resolve => setTimeout(resolve, 2000));
          } catch (error) {
            this.log(`❌ 启用音频失败: ${error.message}`);
          }
          
          // 检查音频轨道
          if (!globalThis.rtc.AudioTrackEvent) {
            this.log('⚠️ 尚未接收到音频轨道信号');
            this.log('💡 机器狗可能不支持音频传输或需要特殊配置');
            this.log('💡 尝试点击"开始播放"按钮强制尝试播放');
            return;
          }
          
          // 检查音频流
          if (!globalThis.rtc.AudioTrackEvent.streams || globalThis.rtc.AudioTrackEvent.streams.length === 0) {
            this.log('❌ 音频流为空');
            return;
          }
          
          const audioStream = globalThis.rtc.AudioTrackEvent.streams[0];
          const audioTracks = audioStream.getAudioTracks();
          
          if (audioTracks.length === 0) {
            this.log('❌ 音频流中没有音频轨道');
            return;
          }
          
          this.log(`✅ 音频接收测试成功！`);
          this.log(`📊 音频轨道数量: ${audioTracks.length}`);
          this.log(`🎵 音频轨道标签: ${audioTracks[0].label || '无标签'}`);
          this.log(`🔊 音频轨道状态: ${audioTracks[0].readyState}`);
          this.log(`⚙️ 音频轨道设置: ${JSON.stringify(audioTracks[0].getSettings())}`);
        }
        
        async startPlayback() {
          try {
            // 检查WebRTC连接和音频轨道
            if (!globalThis.rtc || !globalThis.rtc.connected) {
              this.log('❌ WebRTC未连接，请先建立连接');
              return;
            }
            
            // 如果没有音频轨道，尝试启用音频
            if (!globalThis.rtc.AudioTrackEvent) {
              this.log('⚠️ 未检测到音频流，尝试启用音频传输...');
              try {
                globalThis.rtc.publish("", "on", globalThis.DataChannelType.AUD);
                this.log('📡 已发送音频启用命令，等待音频流...');
                
                // 等待音频轨道建立
                for (let i = 0; i < 10; i++) {
                  await new Promise(resolve => setTimeout(resolve, 500));
                  if (globalThis.rtc.AudioTrackEvent) {
                    this.log('✅ 音频流已建立');
                    break;
                  }
                  this.log(`🔄 等待音频流... (${i + 1}/10)`);
                }
                
                if (!globalThis.rtc.AudioTrackEvent) {
                  this.log('❌ 超时：仍未接收到音频流');
                  this.log('💡 机器狗可能不支持音频传输功能');
                  return;
                }
              } catch (error) {
                this.log(`❌ 启用音频失败: ${error.message}`);
                return;
              }
            }
            
            if (!globalThis.rtc.AudioTrackEvent.streams || globalThis.rtc.AudioTrackEvent.streams.length === 0) {
              this.log('❌ 音频流为空，无法播放');
              return;
            }
            
            const audioStream = globalThis.rtc.AudioTrackEvent.streams[0];
            this.log('🎵 开始播放机器狗音频...');
            
            // 创建音频元素
            if (!this.audioElement) {
              this.audioElement = new Audio();
              this.audioElement.autoplay = true;
              this.audioElement.controls = false;
              
              // 音频事件监听
              this.audioElement.addEventListener('loadstart', () => {
                this.log('📡 开始加载音频流...');
              });
              
              this.audioElement.addEventListener('canplay', () => {
                this.log('✅ 音频流可以播放');
              });
              
              this.audioElement.addEventListener('play', () => {
                this.log('▶️ 音频播放开始');
              });
              
              this.audioElement.addEventListener('error', (e) => {
                this.log(`❌ 音频播放错误: ${e.error?.message || '未知错误'}`);
              });
            }
            
            // 设置音频流
            this.audioElement.srcObject = audioStream;
            
            // 尝试播放
            this.audioElement.play().then(() => {
              this.isPlaying = true;
              this.updatePlaybackUI(true);
              this.startAudioAnalysis(audioStream);
              this.log('✅ 音频播放成功启动');
            }).catch((error) => {
              this.log(`❌ 音频播放失败: ${error.message}`);
              if (error.name === 'NotAllowedError') {
                this.log('💡 浏览器需要用户交互才能播放音频');
                this.log('💡 请确保您已经与页面进行过交互（点击等）');
              }
            });
            
          } catch (error) {
            this.log(`❌ 启动播放失败: ${error.message}`);
            console.error('启动播放失败:', error);
          }
        }
        
        stopPlayback() {
          try {
            if (this.audioElement) {
              this.audioElement.pause();
              this.audioElement.srcObject = null;
            }
            
            if (this.audioContext) {
              this.audioContext.close();
              this.audioContext = null;
            }
            
            // 清理增益节点
            this.gainNode = null;
            
            this.stopAudioAnalysis();
            this.isPlaying = false;
            this.updatePlaybackUI(false);
            
            this.log('⏹️ 音频播放已停止');
            
          } catch (error) {
            this.log(`❌ 停止播放时出错: ${error.message}`);
            console.error('停止播放错误:', error);
          }
        }

        // 清理资源
        destroy() {
          if (this.debugInterval) {
            clearInterval(this.debugInterval);
            this.debugInterval = null;
          }
          this.stopPlayback();
        }
        
        startAudioAnalysis(audioStream) {
          try {
            // 创建音频分析器和增益节点
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.analyser = this.audioContext.createAnalyser();
            this.gainNode = this.audioContext.createGain();
            
            // 设置初始增益
            this.gainNode.gain.value = this.currentGain;
            
            const source = this.audioContext.createMediaStreamSource(audioStream);
            
            // 音频处理链：source -> gainNode -> analyser -> destination
            source.connect(this.gainNode);
            this.gainNode.connect(this.analyser);
            this.gainNode.connect(this.audioContext.destination);
            
            this.analyser.fftSize = 256;
            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);
            
            // 开始音量监控
            this.startAudioLevelMonitoring();
            
            this.log(`🔊 音频增益处理已启用，当前增益: ${(this.currentGain * 100).toFixed(0)}%`);
            
          } catch (error) {
            this.log(`❌ 音频分析初始化失败: ${error.message}`);
            console.error('音频分析错误:', error);
          }
        }
        
        startAudioLevelMonitoring() {
          this.levelUpdateInterval = setInterval(() => {
            if (this.analyser && this.dataArray && this.isPlaying) {
              this.analyser.getByteFrequencyData(this.dataArray);
              
              // 计算平均音量
              let sum = 0;
              for (let i = 0; i < this.dataArray.length; i++) {
                sum += this.dataArray[i];
              }
              const average = sum / this.dataArray.length;
              const percentage = Math.round((average / 255) * 100);
              
              // 更新UI
              if (this.audioLevelFill) {
                this.audioLevelFill.style.width = `${percentage}%`;
              }
              if (this.audioLevelText) {
                this.audioLevelText.textContent = `${percentage}%`;
              }
            }
          }, 100);
        }
        
        stopAudioAnalysis() {
          if (this.levelUpdateInterval) {
            clearInterval(this.levelUpdateInterval);
            this.levelUpdateInterval = null;
          }
          
          if (this.audioLevelFill) {
            this.audioLevelFill.style.width = '0%';
          }
          if (this.audioLevelText) {
            this.audioLevelText.textContent = '0%';
          }
        }

        updateGain(value) {
          this.currentGain = Math.min(value / 100, 5.0); // 最大500%增益
          if (this.gainNode) {
            this.gainNode.gain.value = this.currentGain;
          }
          if (this.gainText) {
            this.gainText.textContent = `${value}%`;
          }
          
          this.log(`📢 增益调节至: ${value}%`);
        }
        
        updatePlaybackUI(playing) {
          try {
            if (playing) {
              if (this.startBtn) {
                this.startBtn.style.display = 'none';
                this.startBtn.disabled = true;
              }
              if (this.stopBtn) {
                this.stopBtn.style.display = 'flex';
                this.stopBtn.disabled = false;
              }
              if (this.audioStatus) {
                this.audioStatus.style.display = 'block';
              }
              if (this.statusText) {
                this.statusText.textContent = '🎵 正在播放机器狗音频...';
              }
            } else {
              if (this.startBtn) {
                this.startBtn.style.display = 'flex';
                this.startBtn.disabled = false;
              }
              if (this.stopBtn) {
                this.stopBtn.style.display = 'none';
                this.stopBtn.disabled = true;
              }
              if (this.audioStatus) {
                this.audioStatus.style.display = 'none';
              }
            }
          } catch (error) {
            console.error('更新播放UI时出错:', error);
          }
        }
        
        debugAudioStatus() {
          // 静默调试，只在控制台输出，不污染UI日志
          if (globalThis.rtc && globalThis.rtc.connected) {
            const hasAudioTrack = !!globalThis.rtc.AudioTrackEvent;
            const audioTrackCount = hasAudioTrack ? globalThis.rtc.AudioTrackEvent.streams[0]?.getAudioTracks().length || 0 : 0;
            console.log(`[音频调试] WebRTC连接: ✅, 音频轨道: ${hasAudioTrack ? '✅' : '❌'}, 轨道数: ${audioTrackCount}`);
            
            if (hasAudioTrack && audioTrackCount > 0) {
              const track = globalThis.rtc.AudioTrackEvent.streams[0].getAudioTracks()[0];
              console.log(`[音频调试] 轨道状态: ${track.readyState}, 标签: ${track.label || '无'}`);
            }
          }
        }

        log(message) {
          const timestamp = new Date().toLocaleTimeString();
          if (this.logContent) {
            this.logContent.innerHTML += `<div style="margin: 2px 0; padding: 2px; border-left: 3px solid #ff6b35; padding-left: 8px;">[${timestamp}] ${message}</div>`;
            this.logContent.scrollTop = this.logContent.scrollHeight;
          }
          
          // 也在控制台输出
          console.log(`[音频播放] ${message}`);
        }
      }
      
              // 页面加载完成后初始化录音功能
        document.addEventListener('DOMContentLoaded', () => {
          try {
            window.micRecorder = new MicrophoneRecorder();
            window.robotAudioPlayer = new RobotAudioPlayer();
            console.log('🎤 麦克风录音功能已初始化');
            console.log('🔊 机器狗音频播放功能已初始化');
            
            // 初始化音量设置显示
            if (window.micRecorder) {
              // 确保UI显示正确的默认值
              if (window.micRecorder.playbackVolumeText) {
                window.micRecorder.playbackVolumeText.textContent = '200%';
              }
              console.log('🎛️ 音量控制已初始化：播放200%');
            }
            
                         // 移动端特殊初始化
             if (window.deviceManager && window.deviceManager.isMobile) {
               console.log('📱 移动端初始化完成');
               
               // 显示移动端使用提示
               setTimeout(() => {
                 if (window.micRecorder) {
                   window.micRecorder.log('📱 移动端优化已启用');
                   window.micRecorder.log('💡 提示：首次使用请点击屏幕激活音频功能');
                   window.micRecorder.log('🔄 支持多设备同时访问，可以随时连接');
                 }
               }, 1000);
             } else if (window.deviceManager) {
               // 桌面端提示
               setTimeout(() => {
                 if (window.micRecorder) {
                   window.micRecorder.log('🖥️ 桌面端已就绪');
                   window.micRecorder.log('🔄 支持多设备同时访问，可以随时连接');
                 }
               }, 1000);
              
              // 添加移动端友好的触摸事件
              document.body.addEventListener('touchstart', () => {
                if (window.mobileAudioManager && !window.mobileAudioManager.isAudioEnabled) {
                  window.mobileAudioManager.enableAudioContext();
                }
              }, { once: true, passive: true });
            }
            
            // 添加错误监听
            window.addEventListener('error', (event) => {
              if (event.error && event.error.message && event.error.message.includes('Maximum call stack')) {
                console.error('检测到栈溢出错误，正在重置录音状态...');
                                if (window.micRecorder) {
                  window.micRecorder.isRecording = false;
                  window.micRecorder.updateRecordingUI(false);
                }
              }
            });
          } catch (error) {
            console.error('初始化音频功能失败:', error);
          }
        });
    </script>

    <!-- 移动端和多设备优化脚本 -->
    <script>
      // 设备和环境检测
      class DeviceManager {
        constructor() {
          this.isMobile = this.detectMobile();
          this.isIOS = this.detectIOS();
          this.deviceId = this.generateDeviceId();
          this.connectionPriority = this.isMobile ? 'low' : 'high';
          
          this.initDeviceOptimizations();
        }
        
        detectMobile() {
          return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                 (navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /MacIntel/.test(navigator.platform));
        }
        
        detectIOS() {
          return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
                 (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
        }
        
        generateDeviceId() {
          let deviceId = localStorage.getItem('device_id');
          if (!deviceId) {
            deviceId = 'device_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            localStorage.setItem('device_id', deviceId);
          }
          return deviceId;
        }
        
        initDeviceOptimizations() {
          if (this.isMobile) {
            // 移动端优化
            document.body.classList.add('mobile-device');
            
            // 禁用一些消耗性能的动画
            document.documentElement.style.setProperty('--animation-duration', '0.2s');
            
            // 优化触摸事件
            document.addEventListener('touchstart', () => {}, { passive: true });
            document.addEventListener('touchmove', () => {}, { passive: true });
            
            // 移动端专用样式
            const mobileStyle = document.createElement('style');
            mobileStyle.textContent = `
              .mobile-device .scanline { display: none; }
              .mobile-device .panel::before { opacity: 0.5; }
              .mobile-device .video-container::before { opacity: 0.2; }
            `;
            document.head.appendChild(mobileStyle);
          }
          
          console.log(`设备检测: ${this.isMobile ? '移动端' : '桌面端'}, ID: ${this.deviceId}`);
        }
      }
      
      // 连接管理器
      class ConnectionManager {
        constructor(deviceManager) {
          this.deviceManager = deviceManager;
          this.connectionState = 'disconnected';
                     this.retryCount = 0;
           this.maxRetries = this.deviceManager.isMobile ? 3 : 3; // 统一重试次数
           this.retryDelay = this.deviceManager.isMobile ? 2000 : 1500; // 减少移动端重试延迟
          
          this.setupConnectionStrategy();
        }
        
                 setupConnectionStrategy() {
           // 优化连接策略，减少移动端和桌面端的差异
           if (this.deviceManager.isMobile) {
             this.connectionTimeout = 12000; // 12秒超时（减少等待时间）
             this.pingInterval = 8000; // 8秒心跳
           } else {
             this.connectionTimeout = 10000; // 10秒超时
             this.pingInterval = 5000; // 5秒心跳
           }
         }
        
        async attemptConnection(connectFunction) {
          const startTime = Date.now();
          
          try {
            // 显示连接状态
            this.updateConnectionStatus('connecting', '正在连接...');
            
            // 移动端连接准备
            if (this.deviceManager.isMobile) {
              await this.checkForExistingConnections();
              // 移除固定延迟，让移动端也能快速连接
            }
            
            const result = await Promise.race([
              connectFunction(),
              new Promise((_, reject) => 
                setTimeout(() => reject(new Error('连接超时')), this.connectionTimeout)
              )
            ]);
            
            this.connectionState = 'connected';
            this.retryCount = 0;
            this.updateConnectionStatus('connected', '已连接');
            
            console.log(`连接成功，耗时: ${Date.now() - startTime}ms`);
            return result;
            
          } catch (error) {
            console.error('连接失败:', error);
            this.connectionState = 'failed';
            
            if (this.retryCount < this.maxRetries) {
              this.retryCount++;
              const delay = this.retryDelay * this.retryCount;
              
              this.updateConnectionStatus('retrying', `连接失败，${delay/1000}秒后重试 (${this.retryCount}/${this.maxRetries})`);
              
              await new Promise(resolve => setTimeout(resolve, delay));
              return this.attemptConnection(connectFunction);
            } else {
              this.updateConnectionStatus('error', '连接失败，请检查网络');
              throw error;
            }
          }
        }
        
        async checkForExistingConnections() {
          // 简化连接检查，移除会阻塞连接的逻辑
          console.log('准备建立连接...');
          // 让每个设备独立连接，由机器狗端处理多连接
        }
        
        updateConnectionStatus(state, message) {
          const statusElement = document.getElementById('status-text');
          const statusDot = document.getElementById('status-dot');
          
          if (statusElement) {
            statusElement.textContent = message;
          }
          
          if (statusDot) {
            statusDot.className = 'status-dot';
            if (state === 'connected') {
              statusDot.classList.add('connected');
            }
          }
        }
      }
      
      // 移动端音频管理器
      class MobileAudioManager {
        constructor(deviceManager) {
          this.deviceManager = deviceManager;
          this.audioContext = null;
          this.isAudioEnabled = false;
          this.permissionState = 'unknown';
          
          if (this.deviceManager.isMobile) {
            this.setupMobileAudioOptimizations();
          }
        }
        
        setupMobileAudioOptimizations() {
          // 移动端音频上下文延迟初始化
          document.addEventListener('touchstart', this.enableAudioContext.bind(this), { once: true });
          document.addEventListener('click', this.enableAudioContext.bind(this), { once: true });
          
          // iOS特殊处理
          if (this.deviceManager.isIOS) {
            this.setupIOSAudioWorkarounds();
          }
        }
        
        async enableAudioContext() {
          if (this.isAudioEnabled) return;
          
          try {
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            if (this.audioContext.state === 'suspended') {
              await this.audioContext.resume();
            }
            
            this.isAudioEnabled = true;
            console.log('移动端音频上下文已启用');
            
            // 触发音频就绪事件
            window.dispatchEvent(new CustomEvent('mobile-audio-ready'));
            
          } catch (error) {
            console.error('启用音频上下文失败:', error);
          }
        }
        
        setupIOSAudioWorkarounds() {
          // iOS Safari 特殊处理
          if (this.deviceManager.isIOS) {
            // 静音音频播放以启用音频权限
            const silentAudio = new Audio();
            silentAudio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmMaBjuBzvDcaywEj2/E9t6TQwsVcJTz7KNYEQrOeqeW4W9oInLMfuTsYhk=';
            silentAudio.volume = 0.01;
            silentAudio.play().catch(() => {});
          }
        }
        
        async requestMicrophonePermission() {
          if (!this.deviceManager.isMobile) {
            return navigator.mediaDevices.getUserMedia({ audio: true });
          }
          
          try {
            // 移动端权限请求优化
            console.log('请求移动端麦克风权限...');
            
            // 确保音频上下文已启用
            await this.enableAudioContext();
            
            // 使用更宽松的音频约束
            const constraints = {
              audio: {
                echoCancellation: false,
                noiseSuppression: false,
                autoGainControl: false,
                channelCount: 1,
                sampleRate: { ideal: 16000, min: 8000, max: 48000 },
                sampleSize: 16
              }
            };
            
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.permissionState = 'granted';
            
            console.log('移动端麦克风权限获取成功');
            return stream;
            
          } catch (error) {
            this.permissionState = 'denied';
            console.error('移动端麦克风权限获取失败:', error);
            
            // 提供用户友好的错误信息
            this.showMobilePermissionGuide(error);
            throw error;
          }
        }
        
        showMobilePermissionGuide(error) {
          const guide = document.createElement('div');
          guide.className = 'mobile-permission-guide';
          guide.innerHTML = `
            <div class="permission-guide-content">
              <h3>🎤 麦克风权限需要您的允许</h3>
              <p>请按以下步骤操作：</p>
              <ol>
                <li>点击地址栏左侧的 🔒 或 ⓘ 图标</li>
                <li>找到"麦克风"选项并允许访问</li>
                <li>刷新页面重试</li>
              </ol>
              <p><small>错误信息: ${error.message}</small></p>
              <button onclick="this.parentElement.parentElement.remove()">我知道了</button>
            </div>
          `;
          
          guide.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
          `;
          
          guide.querySelector('.permission-guide-content').style.cssText = `
            background: var(--glass-bg);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 24px;
            max-width: 400px;
            text-align: center;
            backdrop-filter: blur(20px);
          `;
          
          document.body.appendChild(guide);
        }
      }
      
      // 网络状态管理器
      class NetworkManager {
        constructor(deviceManager) {
          this.deviceManager = deviceManager;
          this.networkQuality = 'unknown';
          this.isOnline = navigator.onLine;
          this.connectionType = this.getConnectionType();
          
          this.setupNetworkMonitoring();
        }
        
        getConnectionType() {
          if (navigator.connection) {
            return navigator.connection.effectiveType || navigator.connection.type || 'unknown';
          }
          return 'unknown';
        }
        
        setupNetworkMonitoring() {
          // 在线状态监听
          window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('网络已连接');
            if (window.micRecorder) {
              window.micRecorder.log('🌐 网络已恢复连接');
            }
          });
          
          window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('网络已断开');
            if (window.micRecorder) {
              window.micRecorder.log('❌ 网络连接中断');
            }
          });
          
          // 网络质量监听
          if (navigator.connection) {
            navigator.connection.addEventListener('change', () => {
              this.connectionType = this.getConnectionType();
              console.log('网络类型变化:', this.connectionType);
              
              if (window.micRecorder) {
                window.micRecorder.log(`📡 网络类型: ${this.connectionType}`);
                
                // 根据网络类型给出建议
                if (this.connectionType === 'slow-2g' || this.connectionType === '2g') {
                  window.micRecorder.log('⚠️ 网络较慢，建议切换到WiFi或4G网络');
                } else if (this.connectionType === '4g' || this.connectionType === 'wifi') {
                  window.micRecorder.log('✅ 网络状况良好');
                }
              }
            });
          }
        }
        
        async testNetworkLatency() {
          const startTime = Date.now();
          try {
            // 使用小的图片文件测试延迟
            await fetch(window.location.origin + '/favicon.ico?' + Math.random(), {
              method: 'HEAD',
              cache: 'no-cache'
            });
            const latency = Date.now() - startTime;
            
            if (latency < 100) {
              this.networkQuality = 'excellent';
            } else if (latency < 300) {
              this.networkQuality = 'good';
            } else if (latency < 500) {
              this.networkQuality = 'fair';
            } else {
              this.networkQuality = 'poor';
            }
            
            console.log(`网络延迟: ${latency}ms, 质量: ${this.networkQuality}`);
            return { latency, quality: this.networkQuality };
            
          } catch (error) {
            this.networkQuality = 'poor';
            console.error('网络测试失败:', error);
            return { latency: -1, quality: 'poor' };
          }
        }
        
        getOptimalSettings() {
          const settings = {
            videoQuality: 'medium',
            audioSampleRate: 16000,
            connectionTimeout: 10000,
            retryDelay: 1500
          };
          
          // 根据设备类型和网络状况调整
          if (this.deviceManager.isMobile) {
            settings.videoQuality = 'low';
            settings.connectionTimeout = 15000;
            settings.retryDelay = 3000;
          }
          
          if (this.networkQuality === 'poor' || this.connectionType === '2g' || this.connectionType === 'slow-2g') {
            settings.videoQuality = 'low';
            settings.audioSampleRate = 8000;
            settings.connectionTimeout = 20000;
            settings.retryDelay = 5000;
          }
          
          return settings;
        }
      }
      
      // 多设备协调管理器
      class MultiDeviceManager {
        constructor(deviceManager) {
          this.deviceManager = deviceManager;
          this.deviceList = this.loadDeviceList();
          this.currentSession = this.createSession();
          
          this.setupDeviceCoordination();
        }
        
        loadDeviceList() {
          try {
            return JSON.parse(localStorage.getItem('device_list') || '[]');
          } catch {
            return [];
          }
        }
        
        saveDeviceList() {
          localStorage.setItem('device_list', JSON.stringify(this.deviceList));
        }
        
        createSession() {
          return {
            deviceId: this.deviceManager.deviceId,
            deviceType: this.deviceManager.isMobile ? 'mobile' : 'desktop',
            startTime: Date.now(),
            lastActivity: Date.now()
          };
        }
        
        setupDeviceCoordination() {
          // 注册当前设备
          this.registerDevice();
          
          // 定期更新活动状态
          setInterval(() => {
            this.updateActivity();
          }, 5000);
          
          // 页面卸载时清理
          window.addEventListener('beforeunload', () => {
            this.unregisterDevice();
          });
        }
        
        registerDevice() {
          const now = Date.now();
          
          // 清理过期设备（超过30秒无活动）
          this.deviceList = this.deviceList.filter(device => 
            now - device.lastActivity < 30000
          );
          
          // 添加或更新当前设备
          const existingIndex = this.deviceList.findIndex(device => 
            device.deviceId === this.currentSession.deviceId
          );
          
          if (existingIndex >= 0) {
            this.deviceList[existingIndex] = this.currentSession;
          } else {
            this.deviceList.push(this.currentSession);
          }
          
          this.saveDeviceList();
          this.logDeviceStatus();
        }
        
        updateActivity() {
          this.currentSession.lastActivity = Date.now();
          this.registerDevice();
        }
        
        unregisterDevice() {
          this.deviceList = this.deviceList.filter(device => 
            device.deviceId !== this.currentSession.deviceId
          );
          this.saveDeviceList();
        }
        
                 logDeviceStatus() {
           const activeDevices = this.deviceList.length;
           const mobileDevices = this.deviceList.filter(d => d.deviceType === 'mobile').length;
           const desktopDevices = this.deviceList.filter(d => d.deviceType === 'desktop').length;
           
           console.log(`设备状态: 总计${activeDevices}个设备 (移动端:${mobileDevices}, 桌面端:${desktopDevices})`);
           
           // 移除会混淆用户的多设备警告，让用户自由选择连接方式
           if (activeDevices > 1) {
             console.log(`多设备访问: ${activeDevices}个设备正在使用平台`);
           }
         }
        
        getConnectionPriority() {
          const desktopDevices = this.deviceList.filter(d => d.deviceType === 'desktop').length;
          
          if (this.deviceManager.isMobile && desktopDevices > 0) {
            return 'low'; // 移动端优先级降低
          }
          
          return 'normal';
        }
        
                 shouldDelayConnection() {
           // 移除连接延迟逻辑，让所有设备都能快速连接
           return false;
         }
      }
      
      // 全局初始化
      window.deviceManager = new DeviceManager();
      window.connectionManager = new ConnectionManager(window.deviceManager);
      window.mobileAudioManager = new MobileAudioManager(window.deviceManager);
      window.networkManager = new NetworkManager(window.deviceManager);
      window.multiDeviceManager = new MultiDeviceManager(window.deviceManager);
      
      console.log('移动端和多设备优化已初始化');
    </script>

    <script type="module" src="index.js"></script>
    
    <!-- 连接功能优化覆盖 -->
    <script>
      // 等待页面加载完成后覆盖连接功能
      document.addEventListener('DOMContentLoaded', () => {
        // 备份原始的连接函数
        const originalHandleConnectClick = window.handleConnectClick;
        
        // 创建优化的连接函数
        window.handleConnectClick = async function() {
          const robotIP = document.getElementById("robot-ip").value;
          const robotToken = document.getElementById("robot-token").value;
          
          // Check if already connected
          if (globalThis.rtc && globalThis.rtc.connected) {
            // Disconnect
            globalThis.rtc.disconnect();
            globalThis.rtc = null;
            updateConnectionStatus(false);
            updateVideoDisplay(false);
            logMessage('已断开与机器人的连接');
            return;
          }
          
          // Validate inputs
          if (!robotIP.trim()) {
            logMessage('错误：请输入机器人IP地址');
            return;
          }
          
          console.log("Robot IP:", robotIP);
          console.log("Robot Token:", robotToken || "(空)");
          console.log(`设备类型: ${window.deviceManager.isMobile ? '移动端' : '桌面端'}`);
          
          // 保存到 localStorage
          localStorage.setItem("robotIP", robotIP);
          localStorage.setItem("robotToken", robotToken);
          
                     // 网络状态检测（不阻塞连接）
           window.networkManager.testNetworkLatency().then(networkTest => {
             console.log('网络测试结果:', networkTest);
             if (networkTest.quality === 'poor') {
               logMessage('⚠️ 网络状况较差，连接可能不稳定');
             }
           }).catch(err => {
             console.log('网络测试失败，继续连接:', err);
           });
           
           // 更新设备状态（仅用于显示，不影响连接）
           if (window.multiDeviceManager) {
             window.multiDeviceManager.registerDevice();
           }
           
           // 使用连接管理器进行连接
           try {
             await window.connectionManager.attemptConnection(async () => {
              // 创建 RTC 连接
              globalThis.rtc = new Go2WebRTC(robotToken, robotIP);
              
              // 设置事件处理器
              globalThis.rtc.onConnectionStateChange = (state) => {
                logMessage(`连接状态: ${state}`);
                if (state === 'connected') {
                  updateConnectionStatus(true);
                  logMessage('✅ 成功连接到机器人！');
                  // 移动端连接成功后的额外处理
                  if (window.deviceManager.isMobile) {
                    logMessage('📱 移动端连接优化已激活');
                  }
                } else if (state === 'disconnected' || state === 'failed') {
                  updateConnectionStatus(false);
                  updateVideoDisplay(false);
                }
              };
              
              // 视频轨道处理（移动端优化）
              globalThis.rtc.onVideoTrack = (stream) => {
                const videoFrame = document.getElementById('video-frame');
                const mobileVideoFrame = document.getElementById('mobile-video-frame');
                logMessage('收到视频流，正在设置...');
                console.log('视频流对象:', stream);
                console.log('视频轨道数量:', stream.getVideoTracks().length);
                
                // 移动端视频优化设置
                const setupVideoElement = (element) => {
                  if (element) {
                    element.muted = true;
                    element.autoplay = true;
                    element.playsInline = true;
                    element.controls = false;
                    element.srcObject = stream;
                    
                    // 移动端特殊优化
                    if (window.deviceManager.isMobile) {
                      element.setAttribute('playsinline', 'true');
                      element.setAttribute('webkit-playsinline', 'true');
                      element.style.objectFit = 'contain';
                    }
                  }
                };
                
                setupVideoElement(videoFrame);
                setupVideoElement(mobileVideoFrame);
                
                // 移动端友好的视频播放
                const playVideo = (element) => {
                  if (!element) return Promise.resolve();
                  
                  return element.play().then(() => {
                    logMessage(`视频流已启动并开始播放 (${element.id})`);
                    updateVideoDisplay(true);
                    console.log('视频播放成功，视频尺寸:', element.videoWidth, 'x', element.videoHeight);
                  }).catch((error) => {
                    logMessage(`视频播放失败 (${element.id}): ${error.message}`);
                    console.error('视频播放错误详情:', error);
                    
                    // 移动端自动播放失败处理
                    if (error.name === 'NotAllowedError' && window.deviceManager.isMobile) {
                      logMessage('📱 移动端需要用户交互才能播放视频，请点击视频区域');
                      
                      // 创建友好的移动端引导
                      const createPlayButton = (container) => {
                        const playBtn = document.createElement('button');
                        playBtn.innerHTML = '▶️ 点击播放视频';
                        playBtn.style.cssText = `
                          position: absolute;
                          top: 50%;
                          left: 50%;
                          transform: translate(-50%, -50%);
                          padding: 12px 24px;
                          background: var(--primary-color);
                          color: var(--bg-primary);
                          border: none;
                          border-radius: 8px;
                          font-size: 1rem;
                          font-weight: 600;
                          cursor: pointer;
                          z-index: 10;
                        `;
                        
                        playBtn.onclick = () => {
                          element.play().then(() => {
                            logMessage('用户交互后视频开始播放');
                            updateVideoDisplay(true);
                            playBtn.remove();
                          }).catch(e => {
                            logMessage(`用户交互后仍无法播放: ${e.message}`);
                          });
                        };
                        
                        container.style.position = 'relative';
                        container.appendChild(playBtn);
                      };
                      
                      const videoContainer = element.closest('.video-container') || element.parentElement;
                      if (videoContainer) {
                        createPlayButton(videoContainer);
                      }
                    }
                    
                    // 即使播放失败，也显示视频元素
                    updateVideoDisplay(true);
                  });
                };
                
                // 延迟播放，移动端延迟更长
                const playDelay = window.deviceManager.isMobile ? 100 : 50;
                setTimeout(() => {
                  playVideo(videoFrame);
                  playVideo(mobileVideoFrame);
                }, playDelay);
                
                // 添加视频事件监听器
                const addVideoEventListeners = (element) => {
                  if (!element) return;
                  
                  element.addEventListener('loadedmetadata', () => {
                    console.log(`视频元数据加载完成 (${element.id}):`, element.videoWidth, 'x', element.videoHeight);
                  });
                  
                  element.addEventListener('canplay', () => {
                    console.log(`视频可以播放 (${element.id})`);
                  });
                  
                  element.addEventListener('playing', () => {
                    console.log(`视频正在播放 (${element.id})`);
                    updateVideoDisplay(true);
                  });
                  
                  element.addEventListener('error', (e) => {
                    console.error(`视频错误 (${element.id}):`, e);
                  });
                };
                
                addVideoEventListeners(videoFrame);
                addVideoEventListeners(mobileVideoFrame);
              };
              
              // 音频轨道处理
              globalThis.rtc.onAudioTrack = (stream) => {
                logMessage('收到机器狗音频流');
                console.log('音频流对象:', stream);
                console.log('音频轨道数量:', stream.getAudioTracks().length);
                
                const audioTracks = stream.getAudioTracks();
                if (audioTracks.length > 0) {
                  const track = audioTracks[0];
                  logMessage(`音频轨道: ${track.label || '未命名'}, 状态: ${track.readyState}`);
                  console.log('音频轨道设置:', track.getSettings());
                }
                
                // 通知音频播放器
                if (window.robotAudioPlayer) {
                  window.robotAudioPlayer.log('✅ 检测到机器狗音频流，可以开始播放');
                  window.robotAudioPlayer.log(`📊 音频轨道: ${audioTracks.length}个, 状态: ${audioTracks[0]?.readyState || '未知'}`);
                }
              };
              
              // 初始化 SDP
              globalThis.rtc.initSDP();
              
              // 返回成功标志
              return true;
            });
                        
                    } catch (error) {
            logMessage(`连接失败: ${error.message}`);
            updateConnectionStatus(false);
            
            // 移动端特殊错误处理
            if (window.deviceManager.isMobile && error.message.includes('timeout')) {
              logMessage('💡 移动端网络连接可能较慢，请检查WiFi信号强度');
            }
          }
        };
        
                 console.log('连接功能已优化');
         
         // 初始化设备状态面板
         setTimeout(() => {
           updateDeviceStatusPanel();
         }, 1000);
       });
       
       // 更新设备状态面板
       function updateDeviceStatusPanel() {
         const panel = document.getElementById('device-status-panel');
         const deviceIcon = document.getElementById('device-icon');
         const deviceType = document.getElementById('device-type');
         const networkIcon = document.getElementById('network-icon');
         const networkStatus = document.getElementById('network-status');
         const deviceInfo = document.querySelector('.device-info');
         const networkInfo = document.querySelector('.network-info');
         
         if (!panel || !window.deviceManager) return;
         
         // 更新设备信息
         if (window.deviceManager.isMobile) {
           deviceIcon.className = 'fas fa-mobile-alt';
           deviceType.textContent = window.deviceManager.isIOS ? 'iOS设备' : '移动端';
           deviceInfo.classList.add('mobile');
                } else {
           deviceIcon.className = 'fas fa-desktop';
           deviceType.textContent = '桌面端';
           deviceInfo.classList.remove('mobile');
         }
         
         // 更新网络信息
         if (window.networkManager) {
           const connectionType = window.networkManager.connectionType;
           const networkQuality = window.networkManager.networkQuality;
           
           let networkText = '';
           let networkClass = '';
           
           if (connectionType !== 'unknown') {
             networkText = connectionType.toUpperCase();
           }
           
           if (networkQuality === 'excellent' || networkQuality === 'good') {
             networkClass = 'good';
             networkIcon.className = 'fas fa-wifi';
           } else if (networkQuality === 'poor') {
             networkClass = 'poor';
             networkIcon.className = 'fas fa-exclamation-triangle';
           } else {
             networkIcon.className = 'fas fa-question-circle';
           }
           
           networkStatus.textContent = networkText || '检测中';
           networkInfo.className = `network-info ${networkClass}`;
         }
         
         // 显示面板
         panel.style.display = 'flex';
         
         // 定期更新
         setTimeout(updateDeviceStatusPanel, 10000);
       }
       
       // 添加连接状态快速检查功能
       function quickConnectionTest() {
         if (window.micRecorder) {
           window.micRecorder.log('🔍 快速连接检查...');
           window.micRecorder.log(`📱 设备类型: ${window.deviceManager?.isMobile ? '移动端' : '桌面端'}`);
           window.micRecorder.log(`🌐 网络状态: ${navigator.onLine ? '在线' : '离线'}`);
           
           // 检查WebRTC支持
           if (window.RTCPeerConnection) {
             window.micRecorder.log('✅ WebRTC 支持正常');
           } else {
             window.micRecorder.log('❌ WebRTC 不支持');
           }
           
           // 检查音频支持
           if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
             window.micRecorder.log('✅ 音频设备支持正常');
           } else {
             window.micRecorder.log('❌ 音频设备不支持');
           }
           
           window.micRecorder.log('💡 多设备访问已优化，可以同时连接');
         }
       }
       
               // 页面加载完成后3秒进行快速检查
        setTimeout(quickConnectionTest, 3000);
      </script>

      <!-- 键盘控制功能脚本 -->
      <script>
        // 键盘控制管理器
        class KeyboardController {
          constructor() {
            this.isActive = false;
            this.isMinimized = false;
            this.activeKeys = new Set();
            this.currentCommand = { x: 0, y: 0, z: 0 };
            this.moveSpeed = 0.5;
            this.turnSpeed = 0.3;
            this.lastCommandTime = 0;
            this.commandThrottle = 100; // 100ms节流
            
            // 键位映射
            this.keyMappings = {
              'KeyW': { action: 'forward', display: 'W', axis: 'x', value: 1 },
              'KeyS': { action: 'backward', display: 'S', axis: 'x', value: -1 },
              'KeyA': { action: 'turnLeft', display: 'A', axis: 'z', value: 1 },
              'KeyD': { action: 'turnRight', display: 'D', axis: 'z', value: -1 },
              'KeyQ': { action: 'strafeLeft', display: 'Q', axis: 'y', value: 1 },
              'KeyE': { action: 'strafeRight', display: 'E', axis: 'y', value: -1 }
            };
            
            this.initEventListeners();
            this.setupDragHandler();
          }
          
          initEventListeners() {
            // 窗口控制按钮
            document.getElementById('minimize-keyboard-btn')?.addEventListener('click', () => this.minimizeWindow());
            document.getElementById('close-keyboard-btn')?.addEventListener('click', () => this.closeWindow());
            document.getElementById('restore-keyboard-btn')?.addEventListener('click', () => this.restoreWindow());
            
            // 速度控制
            const speedSlider = document.getElementById('keyboard-speed');
            const turnSpeedSlider = document.getElementById('keyboard-turn-speed');
            
            speedSlider?.addEventListener('input', (e) => {
              this.moveSpeed = parseFloat(e.target.value);
              document.getElementById('keyboard-speed-text').textContent = `${Math.round(this.moveSpeed * 100)}%`;
            });
            
            turnSpeedSlider?.addEventListener('input', (e) => {
              this.turnSpeed = parseFloat(e.target.value);
              document.getElementById('keyboard-turn-speed-text').textContent = `${Math.round(this.turnSpeed * 100)}%`;
            });
            
            // 键盘事件监听
            document.addEventListener('keydown', (e) => this.handleKeyDown(e));
            document.addEventListener('keyup', (e) => this.handleKeyUp(e));
            
            // 防止页面失去焦点时键盘状态异常
            window.addEventListener('blur', () => this.resetAllKeys());
            window.addEventListener('focus', () => this.resetAllKeys());
          }
          
          setupDragHandler() {
            // 使窗口可拖动
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };
            
            const header = document.querySelector('.keyboard-window-header');
            const window = document.getElementById('keyboard-control-window');
            
            if (header && window) {
              header.addEventListener('mousedown', (e) => {
                isDragging = true;
                const rect = window.getBoundingClientRect();
                dragOffset.x = e.clientX - rect.left;
                dragOffset.y = e.clientY - rect.top;
                document.body.style.userSelect = 'none';
              });
              
              document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                  const x = e.clientX - dragOffset.x;
                  const y = e.clientY - dragOffset.y;
                  
                  // 限制在视窗内
                  const maxX = window.innerWidth - window.offsetWidth;
                  const maxY = window.innerHeight - window.offsetHeight;
                  
                  window.style.left = `${Math.max(0, Math.min(x, maxX))}px`;
                  window.style.top = `${Math.max(0, Math.min(y, maxY))}px`;
                  window.style.right = 'auto';
                }
              });
              
              document.addEventListener('mouseup', () => {
                isDragging = false;
                document.body.style.userSelect = '';
              });
            }
          }
          
          handleKeyDown(e) {
            if (!this.isActive) return;
            
            // 防止在输入框中触发
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            const mapping = this.keyMappings[e.code];
            if (mapping && !this.activeKeys.has(e.code)) {
              e.preventDefault();
              this.activeKeys.add(e.code);
              this.updateKeyVisual(mapping.display.toLowerCase(), true);
              this.updateMovement();
            }
          }
          
          handleKeyUp(e) {
            if (!this.isActive) return;
            
            const mapping = this.keyMappings[e.code];
            if (mapping && this.activeKeys.has(e.code)) {
              e.preventDefault();
              this.activeKeys.delete(e.code);
              this.updateKeyVisual(mapping.display.toLowerCase(), false);
              this.updateMovement();
            }
          }
          
          updateKeyVisual(key, isActive) {
            // 使用更通用的方法查找键位元素
            const keyMappings = document.querySelectorAll('.key-mapping');
            keyMappings.forEach(mapping => {
              const kbdElement = mapping.querySelector('kbd');
              if (kbdElement && kbdElement.textContent.toLowerCase() === key.toLowerCase()) {
                mapping.classList.toggle('active', isActive);
              }
            });
            
            // 更新最小化状态的按键显示
            this.updateMinimizedKeys();
          }
          
          updateMinimizedKeys() {
            const activeKeysDisplay = document.getElementById('active-keys-display');
            if (!activeKeysDisplay) return;
            
            activeKeysDisplay.innerHTML = '';
            this.activeKeys.forEach(keyCode => {
              const mapping = this.keyMappings[keyCode];
              if (mapping) {
                const keySpan = document.createElement('span');
                keySpan.className = 'active-key';
                keySpan.textContent = mapping.display;
                activeKeysDisplay.appendChild(keySpan);
              }
            });
          }
          
          updateMovement() {
            const now = Date.now();
            if (now - this.lastCommandTime < this.commandThrottle) return;
            
            let x = 0, y = 0, z = 0;
            
            // 遍历所有激活的按键，计算各轴的合成值
            this.activeKeys.forEach(keyCode => {
              const mapping = this.keyMappings[keyCode];
              if (mapping) {
                const speed = mapping.axis === 'z' ? this.turnSpeed : this.moveSpeed;
                const value = mapping.value * speed;
                
                switch (mapping.axis) {
                  case 'x': // 前后移动
                    x += value;
                    break;
                  case 'y': // 左右平移
                    y += value;
                    break;
                  case 'z': // 转向
                    z += value;
                    break;
                }
              }
            });
            
            this.currentCommand = { x, y, z };
            this.lastCommandTime = now;
            
            // 更新显示
            this.updateCommandDisplay();
            
            // 发送控制命令
            this.sendMovementCommand();
          }
          
          updateCommandDisplay() {
            const display = document.getElementById('current-command-display');
            if (display) {
              if (this.currentCommand.x === 0 && this.currentCommand.y === 0 && this.currentCommand.z === 0) {
                display.textContent = '无';
                display.style.color = 'var(--text-secondary)';
              } else {
                const parts = [];
                
                // 前后移动
                if (this.currentCommand.x > 0) parts.push('前进');
                if (this.currentCommand.x < 0) parts.push('后退');
                
                // 左右平移
                if (this.currentCommand.y > 0) parts.push('左走');
                if (this.currentCommand.y < 0) parts.push('右走');
                
                // 转向
                if (this.currentCommand.z > 0) parts.push('左转');
                if (this.currentCommand.z < 0) parts.push('右转');
                
                display.textContent = parts.join(' + ') || '停止';
                display.style.color = 'var(--primary-color)';
              }
            }
          }
          
          sendMovementCommand() {
            if (!globalThis.rtc || !globalThis.rtc.connected) {
              console.warn('未连接到机器人，无法发送移动命令');
              return;
            }
            
            try {
              globalThis.rtc.publishApi("rt/api/sport/request", 1008, JSON.stringify(this.currentCommand));
              console.log("键盘控制命令:", this.currentCommand);
            } catch (error) {
              console.error('发送移动命令失败:', error);
            }
          }
          
          resetAllKeys() {
            this.activeKeys.clear();
            
            // 重置所有按键视觉状态
            document.querySelectorAll('.key-mapping').forEach(element => {
              element.classList.remove('active');
            });
            
            // 发送停止命令
            this.currentCommand = { x: 0, y: 0, z: 0 };
            this.updateCommandDisplay();
            this.sendMovementCommand();
            this.updateMinimizedKeys();
          }
          
          activate() {
            this.isActive = true;
            this.showWindow();
            this.updateConnectionStatus();
            
            console.log('键盘控制已激活');
            if (window.micRecorder) {
              window.micRecorder.log('⌨️ 键盘控制模式已启用');
              window.micRecorder.log('💡 控制键位: W前进 S后退 | A左转 D右转 | Q左走 E右走');
            }
          }
          
          deactivate() {
            this.isActive = false;
            this.resetAllKeys();
            this.hideWindow();
            
            console.log('键盘控制已停用');
            if (window.micRecorder) {
              window.micRecorder.log('⌨️ 键盘控制模式已关闭');
            }
          }
          
          showWindow() {
            const window = document.getElementById('keyboard-control-window');
            const minimized = document.getElementById('keyboard-control-minimized');
            
            if (window) {
              window.style.display = 'block';
              // 触发动画
              window.style.animation = 'slideInRight 0.5s ease-out';
            }
            
            if (minimized) {
              minimized.style.display = 'none';
            }
            
            this.isMinimized = false;
          }
          
          hideWindow() {
            const window = document.getElementById('keyboard-control-window');
            const minimized = document.getElementById('keyboard-control-minimized');
            
            if (window) {
              window.style.display = 'none';
            }
            
            if (minimized) {
              minimized.style.display = 'none';
            }
          }
          
          minimizeWindow() {
            const window = document.getElementById('keyboard-control-window');
            const minimized = document.getElementById('keyboard-control-minimized');
            
            if (window) {
              window.style.display = 'none';
            }
            
            if (minimized) {
              minimized.style.display = 'block';
            }
            
            this.isMinimized = true;
          }
          
          restoreWindow() {
            this.showWindow();
          }
          
          closeWindow() {
            this.deactivate();
          }
          
          updateConnectionStatus() {
            const connectionIcon = document.getElementById('keyboard-connection-icon');
            const connectionText = document.getElementById('keyboard-connection-text');
            const connectionStatus = document.querySelector('.connection-status');
            
            if (globalThis.rtc && globalThis.rtc.connected) {
              if (connectionIcon) connectionIcon.className = 'fas fa-plug';
              if (connectionText) connectionText.textContent = '已连接';
              if (connectionStatus) connectionStatus.classList.add('connected');
            } else {
              if (connectionIcon) connectionIcon.className = 'fas fa-unlink';
              if (connectionText) connectionText.textContent = '未连接';
              if (connectionStatus) connectionStatus.classList.remove('connected');
            }
          }
        }
        
        // 初始化键盘控制器
        window.keyboardController = new KeyboardController();
        
        // 监听连接状态变化
        const originalUpdateConnectionStatus = window.updateConnectionStatus;
        window.updateConnectionStatus = function(connected, message) {
          if (originalUpdateConnectionStatus) {
            originalUpdateConnectionStatus(connected, message);
          }
          
          // 更新键盘控制窗口的连接状态
          if (window.keyboardController) {
            window.keyboardController.updateConnectionStatus();
          }
        };
        
        console.log('键盘控制器已初始化');
    </script>

      <!-- 操控模式选择处理脚本 -->
      <script>
        // 操控模式选择管理器
        class ControlModeManager {
          constructor() {
            this.currentMode = null;
            this.setupModeSelection();
          }
          
          setupModeSelection() {
            // 等待DOM加载完成
            document.addEventListener('DOMContentLoaded', () => {
              this.initModeButtons();
            });
            
            // 如果DOM已经加载完成
            if (document.readyState === 'loading') {
              document.addEventListener('DOMContentLoaded', () => {
                this.initModeButtons();
              });
            } else {
              // DOM已经加载完成，直接初始化
              setTimeout(() => this.initModeButtons(), 100);
            }
          }
          
          initModeButtons() {
            const desktopModeBtn = document.getElementById('desktop-mode-btn');
            const mobileModeBtn = document.getElementById('mobile-mode-btn');
            const modeSelection = document.querySelector('.mode-selection');
            const joyLeft = document.getElementById('joy-left');
            const joyRight = document.getElementById('joy-right');
            
            console.log('初始化操控模式按钮...', {
              desktopModeBtn: !!desktopModeBtn,
              mobileModeBtn: !!mobileModeBtn,
              modeSelection: !!modeSelection
            });
            
            if (desktopModeBtn) {
              desktopModeBtn.addEventListener('click', () => {
                console.log('选择键盘操控模式');
                this.selectKeyboardMode(modeSelection, joyLeft, joyRight);
              });
            }
            
            if (mobileModeBtn) {
              mobileModeBtn.addEventListener('click', () => {
                console.log('选择手机操控模式');
                this.selectMobileMode(modeSelection, joyLeft, joyRight);
              });
            }
          }
          
          selectKeyboardMode(modeSelection, joyLeft, joyRight) {
            this.currentMode = 'keyboard';
            
            // 隐藏模式选择弹窗
            if (modeSelection) {
              modeSelection.style.display = 'none';
            }
            
            // 键盘操控模式：隐藏虚拟摇杆
            if (joyLeft) joyLeft.style.display = 'none';
            if (joyRight) joyRight.style.display = 'none';
            
            // 启用键盘控制器
            if (window.keyboardController) {
              window.keyboardController.activate();
            } else {
              console.warn('键盘控制器未初始化');
            }
            
                         // 记录日志
             if (window.micRecorder) {
               window.micRecorder.log('⌨️ 已选择键盘操控模式');
               window.micRecorder.log('💡 控制键位: W前进 S后退 | A左转 D右转 | Q左走 E右走');
             }
            
            console.log('键盘操控模式已启用');
          }
          
          selectMobileMode(modeSelection, joyLeft, joyRight) {
            this.currentMode = 'mobile';
            
            // 隐藏模式选择弹窗
            if (modeSelection) {
              modeSelection.style.display = 'none';
            }
            
            // 手机操控模式：显示虚拟摇杆
            if (joyLeft) joyLeft.style.display = window.deviceManager?.isMobile ? 'block' : 'none';
            if (joyRight) joyRight.style.display = window.deviceManager?.isMobile ? 'block' : 'none';
            
            // 停用键盘控制器
            if (window.keyboardController) {
              window.keyboardController.deactivate();
            }
            
            // 记录日志
            if (window.micRecorder) {
              window.micRecorder.log('📱 已选择手机操控模式');
              window.micRecorder.log('🕹️ 使用虚拟摇杆控制机器狗');
            }
            
            console.log('手机操控模式已启用');
          }
          
          // 获取当前模式
          getCurrentMode() {
            return this.currentMode;
          }
          
          // 重置模式选择
          resetModeSelection() {
            const modeSelection = document.querySelector('.mode-selection');
            if (modeSelection) {
              modeSelection.style.display = 'flex';
            }
            
            // 停用所有控制器
            if (window.keyboardController) {
              window.keyboardController.deactivate();
            }
            
            // 隐藏所有控制元素
            const joyLeft = document.getElementById('joy-left');
            const joyRight = document.getElementById('joy-right');
            if (joyLeft) joyLeft.style.display = 'none';
            if (joyRight) joyRight.style.display = 'none';
            
            this.currentMode = null;
            console.log('操控模式已重置');
          }
        }
        
        // 初始化操控模式管理器
        window.controlModeManager = new ControlModeManager();
        
        // 为操控模式选择按钮添加事件监听
        document.addEventListener('DOMContentLoaded', () => {
          const controlModeBtn = document.getElementById('control-mode-btn');
          if (controlModeBtn) {
            controlModeBtn.addEventListener('click', () => {
              const modeSelection = document.querySelector('.mode-selection');
              if (modeSelection) {
                const isVisible = modeSelection.style.display !== 'none';
                modeSelection.style.display = isVisible ? 'none' : 'flex';
                
                if (!isVisible) {
                  // 显示模式选择时，记录日志
                  if (window.micRecorder) {
                    window.micRecorder.log('🎮 打开操控模式选择');
                  }
                }
              }
            });
          }
        });
        
        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
          // 快捷键：Ctrl + K 打开键盘控制模式
          if (e.ctrlKey && e.key.toLowerCase() === 'k') {
            e.preventDefault();
            if (window.controlModeManager) {
              const modeSelection = document.querySelector('.mode-selection');
              const joyLeft = document.getElementById('joy-left');
              const joyRight = document.getElementById('joy-right');
              window.controlModeManager.selectKeyboardMode(modeSelection, joyLeft, joyRight);
            }
            
                         if (window.micRecorder) {
               window.micRecorder.log('⌨️ 使用快捷键启用键盘控制 (Ctrl+K)');
               window.micRecorder.log('💡 控制键位: W前进 S后退 | A左转 D右转 | Q左走 E右走');
             }
          }
          
          // 快捷键：Ctrl + M 打开手机控制模式
          if (e.ctrlKey && e.key.toLowerCase() === 'm') {
            e.preventDefault();
            if (window.controlModeManager) {
              const modeSelection = document.querySelector('.mode-selection');
              const joyLeft = document.getElementById('joy-left');
              const joyRight = document.getElementById('joy-right');
              window.controlModeManager.selectMobileMode(modeSelection, joyLeft, joyRight);
            }
            
            if (window.micRecorder) {
              window.micRecorder.log('📱 使用快捷键启用手机控制 (Ctrl+M)');
            }
          }
          
          // 快捷键：Escape 重置模式选择
          if (e.key === 'Escape' && window.controlModeManager) {
            if (window.controlModeManager.getCurrentMode()) {
              e.preventDefault();
              window.controlModeManager.resetModeSelection();
              
              if (window.micRecorder) {
                window.micRecorder.log('🔄 重置操控模式选择 (Esc)');
              }
            }
          }
        });
        
        console.log('操控模式管理器已初始化');
        console.log('💡 快捷键: Ctrl+K=键盘控制, Ctrl+M=手机控制, Esc=重置');
      </script>
    

  </body>
</html>
