# internal api id max
RPC_INTERNAL_API_ID_MAX = 100

# internal api id
RPC_API_ID_INTERNAL_API_VERSION  = 1

# lease api id
RPC_API_ID_LEASE_APPLY = 101
RPC_API_ID_LEASE_RENEWAL = 102

# lease term default
RPC_LEASE_TERM = 1.0

# internal error
RPC_OK = 0
# client error
RPC_ERR_UNKNOWN = 3001
RPC_ERR_CLIENT_SEND = 3102
RPC_ERR_CLIENT_API_NOT_REG = 3103
RPC_ERR_CLIENT_API_TIMEOUT = 3104
RPC_ERR_CLIENT_API_NOT_MATCH = 3105
RPC_ERR_CLIENT_API_DATA = 3106
RPC_ERR_CLIENT_LEASE_INVALID = 3107
# server error
RPC_ERR_SERVER_SEND = 3201
RPC_ERR_SERVER_INTERNAL = 3202
RPC_ERR_SERVER_API_NOT_IMPL = 3203
RPC_ERR_SERVER_API_PARAMETER = 3204
RPC_ERR_SERVER_LEASE_DENIED = 3205
RPC_ERR_SERVER_LEASE_NOT_EXIST = 3206
RPC_ERR_SERVER_LEASE_EXIST = 3207
