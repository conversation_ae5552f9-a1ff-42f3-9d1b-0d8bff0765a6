/**
 * Mobile JoyStick - Modified version for mobile interface
 * Based on joy.js by <PERSON> (<PERSON><PERSON><PERSON>)
 * Modified to work correctly within mobile control interface
 */

let MobileStickStatus = {xPosition: 0, yPosition: 0, x: 0, y: 0, cardinalDirection: "C"};

var MobileJoyStick = function(containerId, options, callback) {
  var title = (options = options || {}).title === undefined ? "joystick" : options.title,
      width = options.width === undefined ? 0 : options.width,
      height = options.height === undefined ? 0 : options.height,
      internalFillColor = options.internalFillColor === undefined ? "#00AA00" : options.internalFillColor,
      internalLineWidth = options.internalLineWidth === undefined ? 2 : options.internalLineWidth,
      internalStrokeColor = options.internalStrokeColor === undefined ? "#003300" : options.internalStrokeColor,
      externalLineWidth = options.externalLineWidth === undefined ? 2 : options.externalLineWidth,
      externalStrokeColor = options.externalStrokeColor === undefined ? "#008000" : options.externalStrokeColor,
      autoReturnToCenter = options.autoReturnToCenter === undefined ? true : options.autoReturnToCenter;
  
  callback = callback || function(StickStatus) {};
  
  var container = document.getElementById(containerId);
  container.style.touchAction = "none";
  
  var canvas = document.createElement("canvas");
  canvas.id = title;
  
  if (width === 0) { width = container.clientWidth; }
  if (height === 0) { height = container.clientHeight; }
  
  canvas.width = width;
  canvas.height = height;
  container.appendChild(canvas);
  
  var context = canvas.getContext("2d");
  var pressed = 0;
  var circumference = 2 * Math.PI;
  var internalRadius = (canvas.width - (canvas.width / 2 + 10)) / 2;
  var maxMoveStick = internalRadius + 5;
  var externalRadius = internalRadius + 30;
  var centerX = canvas.width / 2;
  var centerY = canvas.height / 2;
  var directionHorizontalLimitPos = canvas.width / 10;
  var directionHorizontalLimitNeg = directionHorizontalLimitPos * -1;
  var directionVerticalLimitPos = canvas.height / 10;
  var directionVerticalLimitNeg = directionVerticalLimitPos * -1;
  var movedX = centerX;
  var movedY = centerY;
  
  function drawExternal() {
    context.beginPath();
    context.arc(centerX, centerY, externalRadius, 0, circumference, false);
    context.lineWidth = externalLineWidth;
    context.strokeStyle = externalStrokeColor;
    context.stroke();
  }
  
  function drawInternal() {
    context.beginPath();
    if (movedX < internalRadius) { movedX = maxMoveStick; }
    if ((movedX + internalRadius) > canvas.width) { movedX = canvas.width - maxMoveStick; }
    if (movedY < internalRadius) { movedY = maxMoveStick; }
    if ((movedY + internalRadius) > canvas.height) { movedY = canvas.height - maxMoveStick; }
    
    context.arc(movedX, movedY, internalRadius, 0, circumference, false);
    var grd = context.createRadialGradient(centerX, centerY, 5, centerX, centerY, 200);
    grd.addColorStop(0, internalFillColor);
    grd.addColorStop(1, internalStrokeColor);
    context.fillStyle = grd;
    context.fill();
    context.lineWidth = internalLineWidth;
    context.strokeStyle = internalStrokeColor;
    context.stroke();
  }
  
  function getDirection() {
    var result = "";
    var orizontal = movedX - centerX;
    var vertical = movedY - centerY;
    
    if (vertical >= directionVerticalLimitNeg && vertical <= directionVerticalLimitPos) {
      result = "C";
    }
    if (vertical < directionVerticalLimitNeg) {
      result = "N";
    }
    if (vertical > directionVerticalLimitPos) {
      result = "S";
    }
    
    if (orizontal < directionHorizontalLimitNeg) {
      if (result === "C") {
        result = "W";
      } else {
        result += "W";
      }
    }
    if (orizontal > directionHorizontalLimitPos) {
      if (result === "C") {
        result = "E";
      } else {
        result += "E";
      }
    }
    
    return result;
  }
  
  function onTouchStart(event) {
    pressed = 1;
  }
  
  function onTouchMove(event) {
    if (pressed === 1 && event.targetTouches[0].target === canvas) {
      // 获取容器的边界矩形
      var containerRect = container.getBoundingClientRect();
      
      // 计算相对于容器的坐标
      movedX = event.targetTouches[0].clientX - containerRect.left;
      movedY = event.targetTouches[0].clientY - containerRect.top;
      
      // 重绘摇杆
      context.clearRect(0, 0, canvas.width, canvas.height);
      drawExternal();
      drawInternal();
      
      // 更新状态
      MobileStickStatus.xPosition = movedX;
      MobileStickStatus.yPosition = movedY;
      MobileStickStatus.x = ((movedX - centerX) / maxMoveStick * 100).toFixed();
      MobileStickStatus.y = ((movedY - centerY) / maxMoveStick * 100 * -1).toFixed();
      MobileStickStatus.cardinalDirection = getDirection();
      callback(MobileStickStatus);
    }
  }
  
  function onTouchEnd(event) {
    pressed = 0;
    if (autoReturnToCenter) {
      movedX = centerX;
      movedY = centerY;
    }
    context.clearRect(0, 0, canvas.width, canvas.height);
    drawExternal();
    drawInternal();
    
    MobileStickStatus.xPosition = movedX;
    MobileStickStatus.yPosition = movedY;
    MobileStickStatus.x = ((movedX - centerX) / maxMoveStick * 100).toFixed();
    MobileStickStatus.y = ((movedY - centerY) / maxMoveStick * 100 * -1).toFixed();
    MobileStickStatus.cardinalDirection = getDirection();
    callback(MobileStickStatus);
  }
  
  // 添加触摸事件监听器
  if ("ontouchstart" in document.documentElement) {
    canvas.addEventListener("touchstart", onTouchStart, false);
    document.addEventListener("touchmove", onTouchMove, false);
    document.addEventListener("touchend", onTouchEnd, false);
  } else {
    // 鼠标事件作为备用
    canvas.addEventListener("mousedown", function(event) { pressed = 1; }, false);
    document.addEventListener("mousemove", function(event) {
      if (pressed === 1) {
        var containerRect = container.getBoundingClientRect();
        movedX = event.clientX - containerRect.left;
        movedY = event.clientY - containerRect.top;
        
        context.clearRect(0, 0, canvas.width, canvas.height);
        drawExternal();
        drawInternal();
        
        MobileStickStatus.xPosition = movedX;
        MobileStickStatus.yPosition = movedY;
        MobileStickStatus.x = ((movedX - centerX) / maxMoveStick * 100).toFixed();
        MobileStickStatus.y = ((movedY - centerY) / maxMoveStick * 100 * -1).toFixed();
        MobileStickStatus.cardinalDirection = getDirection();
        callback(MobileStickStatus);
      }
    }, false);
    document.addEventListener("mouseup", function(event) {
      pressed = 0;
      if (autoReturnToCenter) {
        movedX = centerX;
        movedY = centerY;
      }
      context.clearRect(0, 0, canvas.width, canvas.height);
      drawExternal();
      drawInternal();
      
      MobileStickStatus.xPosition = movedX;
      MobileStickStatus.yPosition = movedY;
      MobileStickStatus.x = ((movedX - centerX) / maxMoveStick * 100).toFixed();
      MobileStickStatus.y = ((movedY - centerY) / maxMoveStick * 100 * -1).toFixed();
      MobileStickStatus.cardinalDirection = getDirection();
      callback(MobileStickStatus);
    }, false);
  }
  
  // 初始绘制
  drawExternal();
  drawInternal();
  
  // 公共方法
  this.GetWidth = function() { return canvas.width; };
  this.GetHeight = function() { return canvas.height; };
  this.GetPosX = function() { return movedX; };
  this.GetPosY = function() { return movedY; };
  this.GetX = function() { return ((movedX - centerX) / maxMoveStick * 100).toFixed(); };
  this.GetY = function() { return ((movedY - centerY) / maxMoveStick * 100 * -1).toFixed(); };
  this.GetDir = function() { return getDirection(); };
};