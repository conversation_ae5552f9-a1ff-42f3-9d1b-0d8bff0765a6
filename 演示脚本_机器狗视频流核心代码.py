# ============== 机器狗视频流转接演示脚本 ==============
# 演示重点：ROS2到Web的实时视频流转接技术
# 核心价值：解决机器狗二次开发中的视频流访问难题

# ============== 导入核心库 ==============
import time
import numpy as np
import cv2
from fastapi import FastAPI
from threading import Thread
import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy
from sensor_msgs.msg import Image
from starlette.responses import StreamingResponse

# ============== 全局变量 ==============
app = FastAPI()
latest_frame = [None]  # 存储最新视频帧

# ============== 演示重点1：解决QoS兼容性问题 ==============
class Go2VideoNode(Node):
    """
    机器狗视频转接节点
    核心难点：解决ROS2 QoS兼容性问题
    """
    def __init__(self):
        super().__init__('go2_video_bridge')
        
        # 【技术难点】配置QoS策略解决兼容性问题
        # 原因：机器狗原生相机使用BEST_EFFORT，默认订阅会失败
        qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.BEST_EFFORT,  # 关键：兼容机器狗相机
            history=QoSHistoryPolicy.KEEP_LAST,
            depth=1  # 实时性优化：只保留最新帧
        )
        
        # 创建相机订阅器
        self.camera_sub = self.create_subscription(
            Image,
            '/camera/image_raw',
            self.camera_callback,
            qos_profile  # 使用自定义QoS
        )
    
    # ============== 演示重点2：视频流转接核心算法 ==============
    def camera_callback(self, msg):
        """
        视频转接核心算法
        ROS2 Image -> JPEG -> Web Stream
        """
        # 步骤1：ROS2图像数据转numpy数组
        img = np.frombuffer(msg.data, dtype=np.uint8).reshape(
            (msg.height, msg.width, -1)
        )
        
        # 步骤2：OpenCV编码为JPEG（压缩优化）
        _, jpeg = cv2.imencode('.jpg', img)
        
        # 步骤3：存储为字节流（线程安全）
        latest_frame[0] = jpeg.tobytes()

# ============== 演示重点3：实时流式Web API ==============
@app.get("/camera/stream")
def video_stream():
    """
    核心价值：实时MJPEG视频流
    技术亮点：支持多客户端并发访问
    """
    def generate_frames():
        """帧生成器：无限循环输出视频帧"""
        while True:
            if latest_frame[0] is not None:
                # MJPEG格式：HTTP multipart标准
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + 
                       latest_frame[0] + b'\r\n')
            time.sleep(0.03)  # 33fps控制
    
    return StreamingResponse(
        generate_frames(), 
        media_type='multipart/x-mixed-replace; boundary=frame'
    )

# ============== 演示重点4：多线程架构 ==============
def ros2_worker(node):
    """ROS2工作线程"""
    rclpy.spin(node)

# 启动ROS2节点
rclpy.init()
video_node = Go2VideoNode()

# 【关键】后台线程运行ROS2，主线程运行Web服务
Thread(target=ros2_worker, args=(video_node,), daemon=True).start()

# ============== 演示重点5：Web界面 ==============
@app.get("/")
def demo_page():
    """演示页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head><title>机器狗实时视频</title></head>
    <body style="text-align:center; padding:20px;">
        <h1>机器狗视频流转接演示</h1>
        <img src="/camera/stream" style="width:80%; border:2px solid #333;">
        <p>技术特点：ROS2 → FastAPI → Web实时流</p>
    </body>
    </html>
    """

# ============== 启动服务 ==============
if __name__ == "__main__":
    import uvicorn
    print("🚀 启动机器狗视频流服务...")
    print("📱 访问: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
