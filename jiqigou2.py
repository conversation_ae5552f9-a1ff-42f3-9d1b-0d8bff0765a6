# 机器狗Web控制接口
# 功能：提供HTTP API控制机器狗，实时视频流传输，状态监控
# 作者：[您的名字]
# 日期：[创建日期]

# ============== 导入必要的库 ==============
import time                                    # 时间处理：时间戳、延时等
from fastapi import FastAPI, Body              # FastAPI框架：创建Web API，Body用于解析POST请求体
from threading import Thread                   # 多线程：在后台运行ROS2节点
import rclpy                                   # ROS2 Python客户端库
from rclpy.node import Node                    # ROS2节点基类
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy  # QoS服务质量配置
from std_msgs.msg import String, Int32         # ROS2标准消息类型
from geometry_msgs.msg import Twist            # 速度控制消息类型（线性速度+角速度）
from sensor_msgs.msg import Image              # 图像消息类型
import numpy as np                             # 数值计算库：处理图像数组
import cv2                                     # OpenCV：图像处理和编码
from starlette.responses import StreamingResponse  # 流式HTTP响应：用于视频流输出
from go2_interfaces.msg import Go2State        # 自定义消息类型：机器狗状态信息
import uvicorn                                 # ASGI服务器：运行FastAPI应用
from fastapi.responses import HTMLResponse     # HTML响应：返回网页内容

# ============== FastAPI应用初始化 ==============
app = FastAPI()                               # 创建FastAPI应用实例

# ============== 全局变量定义 ==============
# 机器狗最新状态信息字典
latest_state = {
    "id": "dog01",                            # 机器狗唯一标识符
    "name": "GO2机器狗",                       # 机器狗显示名称
    "type": "dog",                            # 设备类型标识
    "status": "unknown",                      # 当前连接状态：unknown/online/offline
    "battery": 0,                             # 电池电量百分比
    "location": "",                           # 位置信息字符串
    "lastUpdate": ""                          # 最后更新时间戳
}

# 使用列表存储可变数据，便于在函数间共享引用
last_heartbeat = [0]                          # 最后心跳时间戳（用于判断在线状态）
latest_frame = [None]                         # 最新视频帧数据（JPEG格式字节流）

# ============== 演示重点：ROS2桥接节点类 ==============
class Go2BridgeNode(Node):
    """
    机器狗ROS2桥接节点 - 核心技术难点
    功能：连接ROS2系统与Web API，处理状态订阅、图像接收、命令发布
    """

    def __init__(self):
        """初始化ROS2节点，创建订阅器和发布器"""
        super().__init__('go2_bridge')            # 调用父类构造函数，设置节点名称

        # ========== 演示重点1：解决实际工程问题的QoS配置 ==========
        # 配置相机图像订阅的QoS策略 - 这是二次开发的关键技术难点
        # 原因：机器狗原生相机节点使用BEST_EFFORT，直接订阅会失败
        qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.BEST_EFFORT,  # 尽力传输，不保证可靠性
            history=QoSHistoryPolicy.KEEP_LAST,            # 只保留最新消息
            depth=1                                        # 队列深度为1，避免延迟累积
        )

        # ========== 演示重点2：视频流转接的核心订阅器 ==========
        # 创建相机图像订阅器 - 这是视频流转接的入口
        self.camera_sub = self.create_subscription(
            Image,                                # 消息类型：ROS2标准图像消息
            '/camera/image_raw',                  # 话题名称：原始图像话题
            self.camera_callback,                 # 回调函数：处理接收到的图像
            qos_profile                           # 使用上面配置的QoS策略
        )

        # 创建机器狗状态订阅器
        self.state_sub = self.create_subscription(
            Go2State,                             # 消息类型：自定义的机器狗状态消息
            '/go2_states',                        # 话题名称：机器狗状态话题
            self.state_callback,                  # 回调函数：处理接收到的状态消息
            10                                    # 队列大小：最多缓存10条消息
        )

        # 创建速度命令发布器
        self.cmd_pub = self.create_publisher(Twist, '/cmd_vel', 10)

        # 创建动作命令发布器
        self.action_pub = self.create_publisher(Int32, '/go2_action_cmd', 10)

    # ========== 演示重点3：视频流转接的核心算法 ==========
    def camera_callback(self, msg):
        """
        相机图像消息回调函数 - 视频流转接的核心技术
        将ROS2图像消息转换为JPEG格式，供Web视频流使用

        技术难点：
        1. ROS2 Image消息格式转换
        2. 实时性能优化
        3. 内存管理

        参数:
            msg (Image): ROS2图像消息
        """
        # 将ROS图像消息的原始数据转换为numpy数组
        # 假设图像编码为'bgr8'格式（BGR色彩空间，每像素8位）
        img = np.frombuffer(msg.data, dtype=np.uint8).reshape((msg.height, msg.width, -1))

        # 使用OpenCV将图像编码为JPEG格式 - 压缩优化网络传输
        _, jpeg = cv2.imencode('.jpg', img)

        # 将JPEG数据转换为字节流并存储到全局变量 - 线程安全的数据共享
        latest_frame[0] = jpeg.tobytes()

    def state_callback(self, msg):
        """
        机器狗状态消息回调函数
        当接收到机器狗状态消息时被调用，更新全局状态变量

        参数:
            msg (Go2State): 机器狗状态消息
        """
        print("收到go2_states消息", msg)              # 调试输出：显示接收到的消息

        # 更新机器狗状态信息到全局变量
        latest_state["status"] = "online"            # 收到消息说明机器狗在线

        # 使用getattr安全获取消息属性，避免属性不存在时出错
        latest_state["mode"] = getattr(msg, "mode", None)                           # 运行模式
        latest_state["progress"] = getattr(msg, "progress", None)                   # 任务进度
        latest_state["gait_type"] = getattr(msg, "gait_type", None)                 # 步态类型
        latest_state["foot_raise_height"] = getattr(msg, "foot_raise_height", None) # 足部抬起高度
        latest_state["position"] = list(getattr(msg, "position", []))               # 位置坐标（转为列表）
        latest_state["body_height"] = getattr(msg, "body_height", None)             # 身体高度
        latest_state["velocity"] = list(getattr(msg, "velocity", []))               # 速度向量（转为列表）
        latest_state["range_obstacle"] = list(getattr(msg, "range_obstacle", []))   # 障碍物距离数组
        latest_state["foot_force"] = list(getattr(msg, "foot_force", []))           # 足部受力数组
        latest_state["foot_position_body"] = list(getattr(msg, "foot_position_body", []))  # 足部相对身体位置
        latest_state["foot_speed_body"] = list(getattr(msg, "foot_speed_body", []))         # 足部相对身体速度
        latest_state["location"] = str(getattr(msg, "position", []))                # 位置信息（字符串格式）
        latest_state["lastUpdate"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())  # 格式化的更新时间
        last_heartbeat[0] = time.time()               # 记录心跳时间戳，用于判断在线状态



    def send_cmd_vel(self, linear_x, angular_z):
        """
        发送速度控制命令

        参数:
            linear_x (float): 线性速度（前进/后退，单位：m/s）
            angular_z (float): 角速度（左转/右转，单位：rad/s）
        """
        twist = Twist()                           # 创建Twist消息对象
        twist.linear.x = linear_x                 # 设置x方向线性速度
        twist.angular.z = angular_z               # 设置z轴角速度
        self.cmd_pub.publish(twist)               # 发布速度命令

    def send_action(self, action_code):
        """
        发送动作控制命令

        参数:
            action_code (int): 动作代码（如站立、趴下、握手等预定义动作）
        """
        msg = Int32()                             # 创建Int32消息对象
        msg.data = action_code                    # 设置动作代码
        self.action_pub.publish(msg)              # 发布动作命令

# ============== ROS2节点启动函数 ==============
def ros2_spin(node):
    """
    ROS2节点运行函数
    在独立线程中运行ROS2节点的事件循环

    参数:
        node (Node): 要运行的ROS2节点
    """
    rclpy.spin(node)                              # 启动ROS2事件循环，处理消息收发

# ============== 初始化并启动ROS2节点 ==============
rclpy.init()                                      # 初始化ROS2系统
node = Go2BridgeNode()                            # 创建机器狗桥接节点实例

# 在后台守护线程中运行ROS2节点
# daemon=True确保主程序退出时线程也会自动退出
Thread(target=ros2_spin, args=(node,), daemon=True).start()

# ============== 演示重点：FastAPI路由定义 ==============

# ========== 演示重点4：实时视频流API - 最有含金量的部分 ==========
@app.get("/camera/stream")
def video_feed():
    """
    实时视频流API - 项目核心价值体现
    以MJPEG格式提供机器狗相机的实时视频流

    技术难点：
    1. 实时流式传输
    2. 多客户端并发访问
    3. 内存优化和性能调优

    返回:
        StreamingResponse: HTTP流式响应，包含连续的JPEG图像帧
    """
    def generate():
        """
        视频帧生成器函数 - 核心算法
        无限循环生成MJPEG格式的视频帧
        """
        while True:
            if latest_frame[0] is not None:       # 检查是否有可用的视频帧
                # 生成MJPEG格式的帧数据 - 符合HTTP multipart标准
                # 包含帧分隔符、内容类型头和JPEG图像数据
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + latest_frame[0] + b'\r\n')
            time.sleep(0.03)                      # 控制帧率约33fps（1/0.03≈33）

    # 返回流式响应，MIME类型为multipart视频流
    return StreamingResponse(generate(), media_type='multipart/x-mixed-replace; boundary=frame')



@app.get("/api/go2/devices")
def get_devices():
    """获取机器狗设备列表API"""
    # 根据心跳时间判断设备在线状态
    now = time.time()
    status = "online" if now - last_heartbeat[0] < 5 else "offline"
    latest_state["status"] = status
    return {"devices": [latest_state]}

@app.post("/api/go2/cmd_vel")
def send_cmd_vel(linear_x: float = Body(...), angular_z: float = Body(...)):
    """发送速度控制命令API"""
    node.send_cmd_vel(linear_x, angular_z)
    return {"result": "sent"}

@app.post("/api/go2/action")
def send_action(action_code: int = Body(...)):
    """发送动作控制命令API"""
    node.send_action(action_code)
    return {"result": "sent"}



# ============== Web界面路由 ==============
@app.get("/")
def index():
    """
    主页路由
    返回一个简单的HTML页面，用于在浏览器中查看机器狗视频流

    返回:
        HTMLResponse: 包含视频显示界面的HTML页面
    """
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>机器狗视频流</title>
        <style>
            /* 页面样式设置 */
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; text-align: center; }
            h1 { color: #333; }
            .video-container { margin: 20px auto; max-width: 800px; }
            img { width: 100%; border: 1px solid #ddd; border-radius: 4px; }
        </style>
    </head>
    <body>
        <h1>机器狗实时视频</h1>
        <div class="video-container">
            <!-- 视频流图像，src指向视频流API端点 -->
            <img src="/camera/stream" alt="机器狗视频流" />
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content, status_code=200)

# ============== 服务器启动配置 ==============
if __name__ == "__main__":
    """
    主程序入口
    当直接运行此脚本时启动FastAPI服务器
    """
    # 使用uvicorn启动FastAPI应用
    # host="0.0.0.0" 允许外部网络访问（不仅限于localhost）
    # port=8000 设置服务端口为8000
    uvicorn.run(app, host="0.0.0.0", port=8000)
