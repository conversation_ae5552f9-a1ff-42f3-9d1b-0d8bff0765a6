# 机器狗Web控制接口 - 深度优化版本
# 功能：提供HTTP API控制机器狗，高性能实时视频流传输，WebSocket支持，状态监控
# 优化特性：异步视频处理、帧缓冲队列、多客户端管理、自适应帧率
# 作者：[您的名字]
# 日期：[创建日期]

# ============== 导入必要的库 ==============
import time                                    # 时间处理：时间戳、延时等
import asyncio                                 # 异步编程：协程和事件循环
import json                                    # JSON数据处理
import logging                                 # 日志记录
from collections import deque                  # 双端队列：高效的帧缓冲
from concurrent.futures import ThreadPoolExecutor  # 线程池：异步JPEG编码
from threading import Thread, Lock             # 多线程：ROS2节点和线程安全
from typing import Set                         # 类型提示：集合类型

from fastapi import FastAPI, Body, WebSocket, WebSocketDisconnect  # FastAPI框架和WebSocket支持
from fastapi.responses import HTMLResponse, StreamingResponse      # HTTP响应类型
import rclpy                                   # ROS2 Python客户端库
from rclpy.node import Node                    # ROS2节点基类
from rclpy.qos import QoSProfile, QoSReliabilityPolicy, QoSHistoryPolicy  # QoS服务质量配置
from std_msgs.msg import String, Int32         # ROS2标准消息类型
from geometry_msgs.msg import Twist            # 速度控制消息类型（线性速度+角速度）
from sensor_msgs.msg import Image              # 图像消息类型
import numpy as np                             # 数值计算库：处理图像数组
import cv2                                     # OpenCV：图像处理和编码
from go2_interfaces.msg import Go2State        # 自定义消息类型：机器狗状态信息
import uvicorn                                 # ASGI服务器：运行FastAPI应用

# ============== 日志配置 ==============
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============== FastAPI应用初始化 ==============
app = FastAPI(title="机器狗控制系统", description="高性能视频流转发和控制API")

# ============== 全局配置 ==============
class VideoConfig:
    """视频流配置类"""
    JPEG_QUALITY = 85                         # JPEG压缩质量（0-100，越高质量越好但文件越大）
    MAX_FRAME_BUFFER = 10                     # 最大帧缓冲数量
    TARGET_FPS = 30                           # 目标帧率
    ENCODING_THREADS = 2                      # JPEG编码线程数
    WEBSOCKET_PING_INTERVAL = 30              # WebSocket心跳间隔（秒）
    FRAME_TIMEOUT = 5.0                       # 帧超时时间（秒）

# ============== 全局变量定义 ==============
# 机器狗最新状态信息字典
latest_state = {
    "id": "dog01",                            # 机器狗唯一标识符
    "name": "GO2机器狗",                       # 机器狗显示名称
    "type": "dog",                            # 设备类型标识
    "status": "unknown",                      # 当前连接状态：unknown/online/offline
    "battery": 0,                             # 电池电量百分比
    "location": "",                           # 位置信息字符串
    "lastUpdate": ""                          # 最后更新时间戳
}

# 使用列表存储可变数据，便于在函数间共享引用
last_heartbeat = [0]                          # 最后心跳时间戳（用于判断在线状态）

# ============== 高性能视频流管理器 ==============
class VideoStreamManager:
    """
    高性能视频流管理器 - 核心优化组件
    功能：异步帧处理、多客户端管理、自适应帧率
    """

    def __init__(self):
        self.frame_buffer = deque(maxlen=VideoConfig.MAX_FRAME_BUFFER)  # 帧缓冲队列
        self.frame_lock = Lock()                                        # 线程安全锁
        self.executor = ThreadPoolExecutor(max_workers=VideoConfig.ENCODING_THREADS)  # 编码线程池
        self.websocket_clients: Set[WebSocket] = set()                  # WebSocket客户端集合
        self.last_frame_time = 0                                        # 最后帧时间戳
        self.frame_count = 0                                            # 帧计数器
        self.fps_counter = 0                                            # FPS计数器
        self.last_fps_time = time.time()                               # 最后FPS统计时间

        # 性能统计
        self.stats = {
            "frames_processed": 0,
            "frames_dropped": 0,
            "current_fps": 0,
            "connected_clients": 0,
            "encoding_time_avg": 0
        }

        logger.info("VideoStreamManager initialized with buffer size: %d", VideoConfig.MAX_FRAME_BUFFER)

    def add_frame_async(self, img_data: np.ndarray) -> None:
        """
        异步添加新帧到缓冲区
        使用线程池进行JPEG编码，避免阻塞ROS2消息处理

        参数:
            img_data: 原始图像数据（numpy数组）
        """
        current_time = time.time()

        # 帧率控制：避免过快的帧率
        if current_time - self.last_frame_time < (1.0 / VideoConfig.TARGET_FPS):
            self.stats["frames_dropped"] += 1
            return

        self.last_frame_time = current_time

        # 提交编码任务到线程池
        self.executor.submit(self._encode_frame, img_data, current_time)
        # 不等待结果，保持异步特性

    def _encode_frame(self, img_data: np.ndarray, timestamp: float) -> None:
        """
        在独立线程中编码帧

        参数:
            img_data: 图像数据
            timestamp: 时间戳
        """
        try:
            encode_start = time.time()

            # 优化的JPEG编码参数
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, VideoConfig.JPEG_QUALITY,
                           cv2.IMWRITE_JPEG_OPTIMIZE, 1]
            _, jpeg_data = cv2.imencode('.jpg', img_data, encode_params)

            encode_time = time.time() - encode_start

            # 创建帧对象
            frame = {
                'data': jpeg_data.tobytes(),
                'timestamp': timestamp,
                'encode_time': encode_time
            }

            # 线程安全地添加到缓冲区
            with self.frame_lock:
                self.frame_buffer.append(frame)
                self.stats["frames_processed"] += 1

            # 更新编码时间统计
            self._update_encoding_stats(encode_time)

            # 异步通知WebSocket客户端
            if self.websocket_clients:
                asyncio.create_task(self._broadcast_frame(frame))

        except Exception as e:
            logger.error("Frame encoding failed: %s", e)

    def _update_encoding_stats(self, encode_time: float) -> None:
        """更新编码性能统计"""
        # 简单的移动平均
        alpha = 0.1
        self.stats["encoding_time_avg"] = (
            alpha * encode_time + (1 - alpha) * self.stats["encoding_time_avg"]
        )

    def get_latest_frame(self) -> dict:
        """
        获取最新帧（用于HTTP MJPEG流）

        返回:
            最新的帧字典，包含data和timestamp
        """
        with self.frame_lock:
            if self.frame_buffer:
                return self.frame_buffer[-1]
            return None

    async def _broadcast_frame(self, frame: dict) -> None:
        """
        向所有WebSocket客户端广播帧

        参数:
            frame: 帧数据字典
        """
        if not self.websocket_clients:
            return

        # 准备WebSocket消息
        message = {
            'type': 'frame',
            'timestamp': frame['timestamp'],
            'data': frame['data'].hex()  # 转换为十六进制字符串传输
        }

        # 并发发送给所有客户端
        disconnected_clients = set()

        for client in self.websocket_clients.copy():
            try:
                await client.send_text(json.dumps(message))
            except Exception as e:
                logger.warning("Failed to send frame to WebSocket client: %s", e)
                disconnected_clients.add(client)

        # 清理断开的连接
        self.websocket_clients -= disconnected_clients
        self.stats["connected_clients"] = len(self.websocket_clients)

    async def add_websocket_client(self, websocket: WebSocket) -> None:
        """添加WebSocket客户端"""
        self.websocket_clients.add(websocket)
        self.stats["connected_clients"] = len(self.websocket_clients)
        logger.info("WebSocket client connected. Total clients: %d", len(self.websocket_clients))

    async def remove_websocket_client(self, websocket: WebSocket) -> None:
        """移除WebSocket客户端"""
        self.websocket_clients.discard(websocket)
        self.stats["connected_clients"] = len(self.websocket_clients)
        logger.info("WebSocket client disconnected. Total clients: %d", len(self.websocket_clients))

    def get_stats(self) -> dict:
        """获取性能统计信息"""
        current_time = time.time()
        time_diff = current_time - self.last_fps_time

        if time_diff >= 1.0:  # 每秒更新一次FPS
            self.stats["current_fps"] = self.fps_counter / time_diff
            self.fps_counter = 0
            self.last_fps_time = current_time

        return self.stats.copy()

# 创建全局视频流管理器实例
video_manager = VideoStreamManager()

# ============== 演示重点：ROS2桥接节点类 ==============
class Go2BridgeNode(Node):
    """
    机器狗ROS2桥接节点 - 核心技术难点
    功能：连接ROS2系统与Web API，处理状态订阅、图像接收、命令发布
    """

    def __init__(self):
        """初始化ROS2节点，创建订阅器和发布器"""
        super().__init__('go2_bridge')            # 调用父类构造函数，设置节点名称

        # ========== 演示重点1：解决实际工程问题的QoS配置 ==========
        # 配置相机图像订阅的QoS策略 - 这是二次开发的关键技术难点
        # 原因：机器狗原生相机节点使用BEST_EFFORT，直接订阅会失败
        qos_profile = QoSProfile(
            reliability=QoSReliabilityPolicy.BEST_EFFORT,  # 尽力传输，不保证可靠性
            history=QoSHistoryPolicy.KEEP_LAST,            # 只保留最新消息
            depth=1                                        # 队列深度为1，避免延迟累积
        )

        # ========== 演示重点2：视频流转接的核心订阅器 ==========
        # 创建相机图像订阅器 - 这是视频流转接的入口
        self.camera_sub = self.create_subscription(
            Image,                                # 消息类型：ROS2标准图像消息
            '/camera/image_raw',                  # 话题名称：原始图像话题
            self.camera_callback,                 # 回调函数：处理接收到的图像
            qos_profile                           # 使用上面配置的QoS策略
        )

        # 创建机器狗状态订阅器
        self.state_sub = self.create_subscription(
            Go2State,                             # 消息类型：自定义的机器狗状态消息
            '/go2_states',                        # 话题名称：机器狗状态话题
            self.state_callback,                  # 回调函数：处理接收到的状态消息
            10                                    # 队列大小：最多缓存10条消息
        )

        # 创建速度命令发布器
        self.cmd_pub = self.create_publisher(Twist, '/cmd_vel', 10)

        # 创建动作命令发布器
        self.action_pub = self.create_publisher(Int32, '/go2_action_cmd', 10)

    # ========== 演示重点3：优化的视频流转接核心算法 ==========
    def camera_callback(self, msg):
        """
        相机图像消息回调函数 - 优化版视频流转接
        使用异步处理避免阻塞ROS2消息循环

        优化特性：
        1. 非阻塞异步处理
        2. 帧率自适应控制
        3. 内存优化管理
        4. 性能统计监控

        参数:
            msg (Image): ROS2图像消息
        """
        try:
            # 将ROS图像消息的原始数据转换为numpy数组
            # 支持多种图像编码格式
            if msg.encoding == 'bgr8':
                img = np.frombuffer(msg.data, dtype=np.uint8).reshape((msg.height, msg.width, 3))
            elif msg.encoding == 'rgb8':
                img = np.frombuffer(msg.data, dtype=np.uint8).reshape((msg.height, msg.width, 3))
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)  # 转换为BGR格式
            elif msg.encoding == 'mono8':
                img = np.frombuffer(msg.data, dtype=np.uint8).reshape((msg.height, msg.width, 1))
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)  # 转换为BGR格式
            else:
                logger.warning("Unsupported image encoding: %s", msg.encoding)
                return

            # 使用优化的视频管理器异步处理帧
            # 这里不会阻塞ROS2消息处理循环
            video_manager.add_frame_async(img)
            video_manager.fps_counter += 1

        except Exception as e:
            logger.error("Camera callback error: %s", e)

    def state_callback(self, msg):
        """
        机器狗状态消息回调函数
        当接收到机器狗状态消息时被调用，更新全局状态变量

        参数:
            msg (Go2State): 机器狗状态消息
        """
        print("收到go2_states消息", msg)              # 调试输出：显示接收到的消息

        # 更新机器狗状态信息到全局变量
        latest_state["status"] = "online"            # 收到消息说明机器狗在线

        # 使用getattr安全获取消息属性，避免属性不存在时出错
        latest_state["mode"] = getattr(msg, "mode", None)                           # 运行模式
        latest_state["progress"] = getattr(msg, "progress", None)                   # 任务进度
        latest_state["gait_type"] = getattr(msg, "gait_type", None)                 # 步态类型
        latest_state["foot_raise_height"] = getattr(msg, "foot_raise_height", None) # 足部抬起高度
        latest_state["position"] = list(getattr(msg, "position", []))               # 位置坐标（转为列表）
        latest_state["body_height"] = getattr(msg, "body_height", None)             # 身体高度
        latest_state["velocity"] = list(getattr(msg, "velocity", []))               # 速度向量（转为列表）
        latest_state["range_obstacle"] = list(getattr(msg, "range_obstacle", []))   # 障碍物距离数组
        latest_state["foot_force"] = list(getattr(msg, "foot_force", []))           # 足部受力数组
        latest_state["foot_position_body"] = list(getattr(msg, "foot_position_body", []))  # 足部相对身体位置
        latest_state["foot_speed_body"] = list(getattr(msg, "foot_speed_body", []))         # 足部相对身体速度
        latest_state["location"] = str(getattr(msg, "position", []))                # 位置信息（字符串格式）
        latest_state["lastUpdate"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())  # 格式化的更新时间
        last_heartbeat[0] = time.time()               # 记录心跳时间戳，用于判断在线状态



    def send_cmd_vel(self, linear_x, angular_z):
        """
        发送速度控制命令

        参数:
            linear_x (float): 线性速度（前进/后退，单位：m/s）
            angular_z (float): 角速度（左转/右转，单位：rad/s）
        """
        twist = Twist()                           # 创建Twist消息对象
        twist.linear.x = linear_x                 # 设置x方向线性速度
        twist.angular.z = angular_z               # 设置z轴角速度
        self.cmd_pub.publish(twist)               # 发布速度命令

    def send_action(self, action_code):
        """
        发送动作控制命令

        参数:
            action_code (int): 动作代码（如站立、趴下、握手等预定义动作）
        """
        msg = Int32()                             # 创建Int32消息对象
        msg.data = action_code                    # 设置动作代码
        self.action_pub.publish(msg)              # 发布动作命令

# ============== ROS2节点启动函数 ==============
def ros2_spin(node):
    """
    ROS2节点运行函数
    在独立线程中运行ROS2节点的事件循环

    参数:
        node (Node): 要运行的ROS2节点
    """
    rclpy.spin(node)                              # 启动ROS2事件循环，处理消息收发

# ============== 初始化并启动ROS2节点 ==============
rclpy.init()                                      # 初始化ROS2系统
node = Go2BridgeNode()                            # 创建机器狗桥接节点实例

# 在后台守护线程中运行ROS2节点
# daemon=True确保主程序退出时线程也会自动退出
Thread(target=ros2_spin, args=(node,), daemon=True).start()

# ============== 演示重点：FastAPI路由定义 ==============

# ========== 演示重点4：优化的实时视频流API ==========
@app.get("/camera/stream")
def video_feed():
    """
    优化的实时视频流API - HTTP MJPEG格式
    使用高性能帧缓冲和自适应帧率

    优化特性：
    1. 帧缓冲机制避免阻塞
    2. 自适应帧率控制
    3. 内存优化和性能监控
    4. 向后兼容性保证

    返回:
        StreamingResponse: HTTP流式响应，包含连续的JPEG图像帧
    """
    def generate():
        """
        优化的视频帧生成器函数
        使用帧缓冲和智能帧率控制
        """
        last_frame_time = 0
        frame_interval = 1.0 / VideoConfig.TARGET_FPS

        while True:
            current_time = time.time()

            # 帧率控制
            if current_time - last_frame_time < frame_interval:
                time.sleep(0.001)  # 短暂休眠，避免CPU占用过高
                continue

            # 获取最新帧
            frame = video_manager.get_latest_frame()
            if frame and frame['data']:
                # 生成MJPEG格式的帧数据
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame['data'] + b'\r\n')
                last_frame_time = current_time
            else:
                time.sleep(0.01)  # 没有帧时稍长休眠

    # 返回流式响应，MIME类型为multipart视频流
    return StreamingResponse(generate(), media_type='multipart/x-mixed-replace; boundary=frame')

# ========== 新增：WebSocket视频流API ==========
@app.websocket("/camera/websocket")
async def websocket_video_feed(websocket: WebSocket):
    """
    WebSocket实时视频流API - 为你同事提供的高性能接口
    支持双向通信和更低的延迟

    特性：
    1. 低延迟实时传输
    2. 双向通信支持
    3. 自动重连机制
    4. 连接状态监控

    参数:
        websocket: WebSocket连接对象
    """
    await websocket.accept()
    await video_manager.add_websocket_client(websocket)

    try:
        # 发送连接确认消息
        await websocket.send_text(json.dumps({
            'type': 'connected',
            'message': 'WebSocket video stream connected',
            'config': {
                'target_fps': VideoConfig.TARGET_FPS,
                'jpeg_quality': VideoConfig.JPEG_QUALITY
            }
        }))

        # 保持连接活跃，处理客户端消息
        while True:
            try:
                # 等待客户端消息（心跳、配置等）
                message = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=VideoConfig.WEBSOCKET_PING_INTERVAL
                )

                # 处理客户端消息
                try:
                    data = json.loads(message)
                    if data.get('type') == 'ping':
                        await websocket.send_text(json.dumps({'type': 'pong'}))
                    elif data.get('type') == 'get_stats':
                        stats = video_manager.get_stats()
                        await websocket.send_text(json.dumps({
                            'type': 'stats',
                            'data': stats
                        }))
                except json.JSONDecodeError:
                    logger.warning("Invalid JSON message from WebSocket client")

            except asyncio.TimeoutError:
                # 发送心跳
                await websocket.send_text(json.dumps({'type': 'ping'}))

    except WebSocketDisconnect:
        logger.info("WebSocket client disconnected normally")
    except Exception as e:
        logger.error("WebSocket error: %s", e)
    finally:
        await video_manager.remove_websocket_client(websocket)



@app.get("/api/go2/devices")
def get_devices():
    """获取机器狗设备列表API"""
    # 根据心跳时间判断设备在线状态
    now = time.time()
    status = "online" if now - last_heartbeat[0] < 5 else "offline"
    latest_state["status"] = status
    return {"devices": [latest_state]}

@app.post("/api/go2/cmd_vel")
def send_cmd_vel(linear_x: float = Body(...), angular_z: float = Body(...)):
    """发送速度控制命令API"""
    node.send_cmd_vel(linear_x, angular_z)
    return {"result": "sent"}

@app.post("/api/go2/action")
def send_action(action_code: int = Body(...)):
    """发送动作控制命令API"""
    node.send_action(action_code)
    return {"result": "sent"}

# ========== 新增：性能监控API ==========
@app.get("/api/video/stats")
def get_video_stats():
    """
    获取视频流性能统计API
    提供实时性能监控数据

    返回:
        dict: 包含帧率、延迟、连接数等统计信息
    """
    stats = video_manager.get_stats()
    return {
        "video_stats": stats,
        "system_info": {
            "target_fps": VideoConfig.TARGET_FPS,
            "jpeg_quality": VideoConfig.JPEG_QUALITY,
            "buffer_size": VideoConfig.MAX_FRAME_BUFFER,
            "encoding_threads": VideoConfig.ENCODING_THREADS
        },
        "timestamp": time.time()
    }

@app.get("/api/video/config")
def get_video_config():
    """获取视频配置信息API"""
    return {
        "target_fps": VideoConfig.TARGET_FPS,
        "jpeg_quality": VideoConfig.JPEG_QUALITY,
        "max_frame_buffer": VideoConfig.MAX_FRAME_BUFFER,
        "encoding_threads": VideoConfig.ENCODING_THREADS,
        "websocket_ping_interval": VideoConfig.WEBSOCKET_PING_INTERVAL
    }



# ============== Web界面路由 ==============
@app.get("/")
def index():
    """
    主页路由 - 增强版视频流界面
    支持HTTP MJPEG和WebSocket两种方式，包含性能监控

    返回:
        HTMLResponse: 包含视频显示界面和WebSocket支持的HTML页面
    """
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>机器狗视频流 - 深度优化版</title>
        <meta charset="UTF-8">
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0; padding: 20px; background: #f5f5f5;
            }
            .container { max-width: 1200px; margin: 0 auto; }
            h1 { color: #333; text-align: center; margin-bottom: 30px; }
            .video-section {
                display: flex; gap: 20px; margin-bottom: 30px;
                flex-wrap: wrap; justify-content: center;
            }
            .video-container {
                background: white; border-radius: 8px; padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1); flex: 1; min-width: 400px;
            }
            .video-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #555; }
            img, canvas {
                width: 100%; max-width: 500px; border: 2px solid #ddd;
                border-radius: 8px; display: block; margin: 0 auto;
            }
            .controls {
                margin-top: 15px; display: flex; gap: 10px;
                justify-content: center; flex-wrap: wrap;
            }
            button {
                padding: 8px 16px; border: none; border-radius: 4px;
                background: #007bff; color: white; cursor: pointer; font-size: 14px;
            }
            button:hover { background: #0056b3; }
            button:disabled { background: #ccc; cursor: not-allowed; }
            .stats {
                background: white; border-radius: 8px; padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-top: 20px;
            }
            .stats-grid {
                display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px; margin-top: 15px;
            }
            .stat-item {
                background: #f8f9fa; padding: 10px; border-radius: 4px;
                border-left: 4px solid #007bff;
            }
            .stat-label { font-size: 12px; color: #666; margin-bottom: 5px; }
            .stat-value { font-size: 18px; font-weight: bold; color: #333; }
            .status {
                display: inline-block; padding: 4px 8px; border-radius: 12px;
                font-size: 12px; font-weight: bold; margin-left: 10px;
            }
            .status.connected { background: #d4edda; color: #155724; }
            .status.disconnected { background: #f8d7da; color: #721c24; }
            .log {
                background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;
                padding: 10px; height: 150px; overflow-y: auto; font-family: monospace;
                font-size: 12px; margin-top: 15px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🐕 机器狗实时视频流 - 深度优化版</h1>

            <div class="video-section">
                <!-- HTTP MJPEG 视频流 -->
                <div class="video-container">
                    <div class="video-title">📺 HTTP MJPEG 流 (兼容模式)</div>
                    <img id="mjpegStream" src="/camera/stream" alt="机器狗视频流" />
                    <div class="controls">
                        <button onclick="toggleMJPEG()">暂停/恢复</button>
                        <button onclick="refreshMJPEG()">刷新</button>
                    </div>
                </div>

                <!-- WebSocket 视频流 -->
                <div class="video-container">
                    <div class="video-title">
                        ⚡ WebSocket 流 (高性能模式)
                        <span id="wsStatus" class="status disconnected">未连接</span>
                    </div>
                    <canvas id="wsCanvas" width="640" height="480"></canvas>
                    <div class="controls">
                        <button id="wsConnect" onclick="toggleWebSocket()">连接 WebSocket</button>
                        <button onclick="requestStats()">获取统计</button>
                    </div>
                </div>
            </div>

            <!-- 性能统计 -->
            <div class="stats">
                <h3>📊 实时性能监控</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-label">当前帧率 (FPS)</div>
                        <div class="stat-value" id="currentFps">--</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">已处理帧数</div>
                        <div class="stat-value" id="framesProcessed">--</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">丢帧数</div>
                        <div class="stat-value" id="framesDropped">--</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">连接客户端</div>
                        <div class="stat-value" id="connectedClients">--</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均编码时间 (ms)</div>
                        <div class="stat-value" id="avgEncodeTime">--</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">目标帧率</div>
                        <div class="stat-value" id="targetFps">--</div>
                    </div>
                </div>

                <!-- 日志区域 -->
                <h4>📝 连接日志</h4>
                <div id="logArea" class="log"></div>
            </div>
        </div>

        <script>
            // WebSocket 相关变量
            let ws = null;
            let wsCanvas = null;
            let wsCtx = null;
            let isWSConnected = false;

            // 初始化
            document.addEventListener('DOMContentLoaded', function() {
                wsCanvas = document.getElementById('wsCanvas');
                wsCtx = wsCanvas.getContext('2d');

                // 定期更新统计信息
                setInterval(updateStats, 2000);
                updateStats(); // 立即更新一次

                log('页面加载完成，准备就绪');
            });

            // 日志函数
            function log(message) {
                const logArea = document.getElementById('logArea');
                const timestamp = new Date().toLocaleTimeString();
                logArea.innerHTML += `[${timestamp}] ${message}\\n`;
                logArea.scrollTop = logArea.scrollHeight;
            }

            // WebSocket 连接管理
            function toggleWebSocket() {
                if (isWSConnected) {
                    disconnectWebSocket();
                } else {
                    connectWebSocket();
                }
            }

            function connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/camera/websocket`;

                log(`正在连接 WebSocket: ${wsUrl}`);

                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    isWSConnected = true;
                    updateWSStatus('connected');
                    document.getElementById('wsConnect').textContent = '断开 WebSocket';
                    log('WebSocket 连接成功');
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWSMessage(data);
                    } catch (e) {
                        log(`WebSocket 消息解析错误: ${e.message}`);
                    }
                };

                ws.onclose = function(event) {
                    isWSConnected = false;
                    updateWSStatus('disconnected');
                    document.getElementById('wsConnect').textContent = '连接 WebSocket';
                    log(`WebSocket 连接关闭: ${event.code} ${event.reason}`);
                };

                ws.onerror = function(error) {
                    log(`WebSocket 错误: ${error}`);
                };
            }

            function disconnectWebSocket() {
                if (ws) {
                    ws.close();
                    ws = null;
                }
            }

            function updateWSStatus(status) {
                const statusEl = document.getElementById('wsStatus');
                statusEl.className = `status ${status}`;
                statusEl.textContent = status === 'connected' ? '已连接' : '未连接';
            }

            // 处理 WebSocket 消息
            function handleWSMessage(data) {
                switch (data.type) {
                    case 'connected':
                        log(`WebSocket 连接确认: ${data.message}`);
                        break;
                    case 'frame':
                        displayWSFrame(data);
                        break;
                    case 'stats':
                        updateStatsDisplay(data.data);
                        break;
                    case 'ping':
                        // 响应心跳
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({type: 'pong'}));
                        }
                        break;
                }
            }

            // 显示 WebSocket 帧
            function displayWSFrame(frameData) {
                try {
                    // 将十六进制字符串转换为字节数组
                    const hexString = frameData.data;
                    const bytes = new Uint8Array(hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));

                    // 创建 Blob 和 URL
                    const blob = new Blob([bytes], {type: 'image/jpeg'});
                    const url = URL.createObjectURL(blob);

                    // 创建图像并绘制到 canvas
                    const img = new Image();
                    img.onload = function() {
                        wsCtx.clearRect(0, 0, wsCanvas.width, wsCanvas.height);
                        wsCtx.drawImage(img, 0, 0, wsCanvas.width, wsCanvas.height);
                        URL.revokeObjectURL(url);
                    };
                    img.src = url;
                } catch (e) {
                    log(`帧显示错误: ${e.message}`);
                }
            }

            // MJPEG 控制
            let mjpegPaused = false;
            function toggleMJPEG() {
                const img = document.getElementById('mjpegStream');
                if (mjpegPaused) {
                    img.src = '/camera/stream?' + new Date().getTime();
                    mjpegPaused = false;
                } else {
                    img.src = '';
                    mjpegPaused = true;
                }
            }

            function refreshMJPEG() {
                const img = document.getElementById('mjpegStream');
                img.src = '/camera/stream?' + new Date().getTime();
            }

            // 统计信息更新
            function updateStats() {
                fetch('/api/video/stats')
                    .then(response => response.json())
                    .then(data => {
                        updateStatsDisplay(data.video_stats);
                        document.getElementById('targetFps').textContent = data.system_info.target_fps;
                    })
                    .catch(error => {
                        console.error('获取统计信息失败:', error);
                    });
            }

            function updateStatsDisplay(stats) {
                document.getElementById('currentFps').textContent = stats.current_fps.toFixed(1);
                document.getElementById('framesProcessed').textContent = stats.frames_processed;
                document.getElementById('framesDropped').textContent = stats.frames_dropped;
                document.getElementById('connectedClients').textContent = stats.connected_clients;
                document.getElementById('avgEncodeTime').textContent = (stats.encoding_time_avg * 1000).toFixed(1);
            }

            function requestStats() {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({type: 'get_stats'}));
                    log('请求统计信息');
                } else {
                    log('WebSocket 未连接，无法请求统计信息');
                }
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content, status_code=200)

# ============== 服务器启动配置 ==============
if __name__ == "__main__":
    """
    主程序入口
    当直接运行此脚本时启动FastAPI服务器
    """
    # 使用uvicorn启动FastAPI应用
    # host="0.0.0.0" 允许外部网络访问（不仅限于localhost）
    # port=8000 设置服务端口为8000
    uvicorn.run(app, host="0.0.0.0", port=8000)
