#!/usr/bin/env python3
"""
蓝牙超声波音频控制器 - API测试脚本
"""

import requests
import time
import json

class APITester:
    def __init__(self, base_url="http://localhost:5000/api"):
        self.base_url = base_url
    
    def test_connection(self):
        """测试API连接"""
        try:
            response = requests.get(f"{self.base_url}/status", timeout=5)
            if response.status_code == 200:
                print("✓ API服务器连接成功")
                return True
            else:
                print(f"✗ API服务器响应错误: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"✗ 无法连接到API服务器: {e}")
            return False
    
    def test_frequency_control(self):
        """测试频率控制"""
        print("\n=== 测试频率控制 ===")
        
        frequencies = [100, 1000, 5000, 10000, 20000]
        
        for freq in frequencies:
            try:
                # 设置频率
                response = requests.post(f"{self.base_url}/frequency", 
                                       json={"frequency": freq}, timeout=5)
                if response.status_code == 200:
                    print(f"✓ 频率设置为 {freq}Hz")
                else:
                    print(f"✗ 频率设置失败 {freq}Hz: {response.text}")
                
                time.sleep(0.5)
                
            except requests.exceptions.RequestException as e:
                print(f"✗ 频率设置请求失败 {freq}Hz: {e}")
    
    def test_volume_control(self):
        """测试音量控制"""
        print("\n=== 测试音量控制 ===")
        
        volumes = [0.1, 0.3, 0.5, 0.7, 1.0]
        
        for vol in volumes:
            try:
                response = requests.post(f"{self.base_url}/volume", 
                                       json={"volume": vol}, timeout=5)
                if response.status_code == 200:
                    print(f"✓ 音量设置为 {int(vol*100)}%")
                else:
                    print(f"✗ 音量设置失败 {int(vol*100)}%: {response.text}")
                
                time.sleep(0.5)
                
            except requests.exceptions.RequestException as e:
                print(f"✗ 音量设置请求失败 {int(vol*100)}%: {e}")
    
    def test_playback_control(self):
        """测试播放控制"""
        print("\n=== 测试播放控制 ===")
        
        try:
            # 开始播放
            response = requests.post(f"{self.base_url}/play", 
                                   json={"frequency": 1000}, timeout=5)
            if response.status_code == 200:
                print("✓ 开始播放 1000Hz")
            else:
                print(f"✗ 播放失败: {response.text}")
            
            time.sleep(2)
            
            # 暂停
            response = requests.post(f"{self.base_url}/pause", timeout=5)
            if response.status_code == 200:
                print("✓ 暂停播放")
            else:
                print(f"✗ 暂停失败: {response.text}")
            
            time.sleep(1)
            
            # 恢复
            response = requests.post(f"{self.base_url}/resume", timeout=5)
            if response.status_code == 200:
                print("✓ 恢复播放")
            else:
                print(f"✗ 恢复失败: {response.text}")
            
            time.sleep(2)
            
            # 再次暂停
            response = requests.post(f"{self.base_url}/pause", timeout=5)
            if response.status_code == 200:
                print("✓ 停止播放")
            else:
                print(f"✗ 停止失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 播放控制请求失败: {e}")
    
    def test_bluetooth_devices(self):
        """测试蓝牙设备功能"""
        print("\n=== 测试蓝牙设备 ===")
        
        try:
            response = requests.get(f"{self.base_url}/bluetooth/devices", timeout=10)
            if response.status_code == 200:
                data = response.json()
                devices = data.get('devices', [])
                print(f"✓ 发现 {len(devices)} 个蓝牙设备")
                
                for device in devices:
                    status = "已连接" if device['connected'] else "未连接"
                    print(f"  - {device['name']} ({device['address']}) - {status}")
                    
            else:
                print(f"✗ 获取蓝牙设备失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 蓝牙设备请求失败: {e}")
    
    def test_status(self):
        """测试状态获取"""
        print("\n=== 测试状态获取 ===")
        
        try:
            response = requests.get(f"{self.base_url}/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                status = data.get('data', {})
                print("✓ 当前状态:")
                print(f"  - 播放状态: {'播放中' if status.get('is_playing') else '已停止'}")
                print(f"  - 当前频率: {status.get('frequency', 'N/A')}Hz")
                print(f"  - 当前音量: {int(status.get('volume', 0)*100)}%")
                print(f"  - 连接设备: {status.get('connected_device', '无')}")
                print(f"  - 采样率: {status.get('sample_rate', 'N/A')}Hz")
            else:
                print(f"✗ 获取状态失败: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"✗ 状态请求失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("蓝牙超声波音频控制器 - API测试")
        print("=" * 40)
        
        if not self.test_connection():
            print("\n请确保API服务器正在运行:")
            print("python3 bluetooth_ultrasonic_controller.py")
            return
        
        self.test_status()
        self.test_frequency_control()
        self.test_volume_control()
        self.test_bluetooth_devices()
        self.test_playback_control()
        
        print("\n" + "=" * 40)
        print("测试完成！")

def main():
    tester = APITester()
    
    print("选择测试模式:")
    print("1. 运行所有测试")
    print("2. 仅测试连接")
    print("3. 仅测试播放功能")
    print("4. 仅测试蓝牙功能")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        tester.run_all_tests()
    elif choice == "2":
        tester.test_connection()
        tester.test_status()
    elif choice == "3":
        if tester.test_connection():
            tester.test_frequency_control()
            tester.test_volume_control()
            tester.test_playback_control()
    elif choice == "4":
        if tester.test_connection():
            tester.test_bluetooth_devices()
    else:
        print("无效选择")

if __name__ == '__main__':
    main()
