2025-07-23 16:41:08,734 - utils - INFO - [2025-07-23 16:41:08] 机器狗控制API启动
2025-07-23 16:41:49,315 - utils - INFO - [2025-07-23 16:41:49] 收到连接请求: ************
2025-07-23 16:41:49,316 - utils - INFO - [2025-07-23 16:41:49] 开始连接机器狗: ************
2025-07-23 16:41:49,316 - utils - INFO - [2025-07-23 16:41:49] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 16:41:49,327 - utils - ERROR - [2025-07-23 16:41:49] 初始化WebRTC连接失败: 'dict' object has no attribute 'bundlePolicy'
2025-07-23 16:41:49,327 - utils - ERROR - [2025-07-23 16:41:49] 连接机器狗失败: ************
2025-07-23 16:41:49,327 - utils - ERROR - [2025-07-23 16:41:49] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 16:42:28,757 - utils - INFO - [2025-07-23 16:42:28] 收到连接请求: ************
2025-07-23 16:42:28,757 - utils - INFO - [2025-07-23 16:42:28] 开始连接机器狗: ************
2025-07-23 16:42:28,757 - utils - INFO - [2025-07-23 16:42:28] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 16:42:28,757 - utils - ERROR - [2025-07-23 16:42:28] 初始化WebRTC连接失败: 'dict' object has no attribute 'bundlePolicy'
2025-07-23 16:42:28,758 - utils - ERROR - [2025-07-23 16:42:28] 连接机器狗失败: ************
2025-07-23 16:42:28,758 - utils - ERROR - [2025-07-23 16:42:28] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 16:54:25,515 - utils - INFO - [2025-07-23 16:54:25] 机器狗控制API启动
2025-07-23 16:54:45,324 - utils - INFO - [2025-07-23 16:54:45] 机器狗控制API关闭
2025-07-23 16:54:45,324 - utils - INFO - [2025-07-23 16:54:45] 没有活跃的机器狗连接
2025-07-23 16:54:48,019 - utils - INFO - [2025-07-23 16:54:48] 机器狗控制API启动
2025-07-23 16:54:48,595 - utils - INFO - [2025-07-23 16:54:48] 收到连接请求: ************
2025-07-23 16:54:48,595 - utils - INFO - [2025-07-23 16:54:48] 开始连接机器狗: ************
2025-07-23 16:54:48,597 - utils - INFO - [2025-07-23 16:54:48] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 16:54:48,602 - utils - ERROR - [2025-07-23 16:54:48] 初始化WebRTC连接失败: 'dict' object has no attribute 'bundlePolicy'
2025-07-23 16:54:48,602 - utils - ERROR - [2025-07-23 16:54:48] 连接机器狗失败: ************
2025-07-23 16:54:48,603 - utils - ERROR - [2025-07-23 16:54:48] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 16:56:08,122 - utils - INFO - [2025-07-23 16:56:08] 机器狗控制API关闭
2025-07-23 16:56:08,122 - utils - INFO - [2025-07-23 16:56:08] 没有活跃的机器狗连接
2025-07-23 16:56:09,225 - utils - INFO - [2025-07-23 16:56:09] 机器狗控制API启动
2025-07-23 16:56:40,125 - utils - INFO - [2025-07-23 16:56:40] 机器狗控制API关闭
2025-07-23 16:56:40,125 - utils - INFO - [2025-07-23 16:56:40] 没有活跃的机器狗连接
2025-07-23 16:56:41,168 - utils - INFO - [2025-07-23 16:56:41] 机器狗控制API启动
2025-07-23 16:56:57,880 - utils - INFO - [2025-07-23 16:56:57] 机器狗控制API关闭
2025-07-23 16:56:57,880 - utils - INFO - [2025-07-23 16:56:57] 没有活跃的机器狗连接
2025-07-23 16:57:30,057 - utils - INFO - [2025-07-23 16:57:30] 机器狗控制API启动
2025-07-23 16:57:50,254 - utils - INFO - [2025-07-23 16:57:50] 机器狗控制API关闭
2025-07-23 16:57:50,254 - utils - INFO - [2025-07-23 16:57:50] 没有活跃的机器狗连接
2025-07-23 16:57:51,369 - utils - INFO - [2025-07-23 16:57:51] 机器狗控制API启动
2025-07-23 16:58:40,876 - utils - INFO - [2025-07-23 16:58:40] 收到连接请求: ************
2025-07-23 16:58:40,876 - utils - INFO - [2025-07-23 16:58:40] 开始连接机器狗: ************
2025-07-23 16:58:40,876 - utils - INFO - [2025-07-23 16:58:40] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 16:58:40,906 - aioice.ice - INFO - Connection(2) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,907 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,907 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,908 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,908 - aioice.ice - INFO - Connection(0) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,908 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,908 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,909 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,909 - aioice.ice - INFO - Connection(1) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,909 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,909 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,910 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,910 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,910 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:40,911 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 16:58:45,915 - utils - INFO - [2025-07-23 16:58:45] WebRTC Offer已创建
2025-07-23 16:58:45,916 - utils - INFO - [2025-07-23 16:58:45] 发送WebRTC offer到机器狗: ************
2025-07-23 16:58:48,091 - utils - ERROR - [2025-07-23 16:58:48] 信令初始化失败: Cannot connect to host ************:8081 ssl:default [Connect call failed ('************', 8081)]
2025-07-23 16:58:48,091 - utils - ERROR - [2025-07-23 16:58:48] 连接机器狗失败: ************
2025-07-23 16:58:48,091 - utils - ERROR - [2025-07-23 16:58:48] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 17:00:00,436 - utils - INFO - [2025-07-23 17:00:00] 机器狗控制API关闭
2025-07-23 17:00:00,436 - utils - INFO - [2025-07-23 17:00:00] 没有活跃的机器狗连接
2025-07-23 17:00:01,504 - utils - INFO - [2025-07-23 17:00:01] 机器狗控制API启动
2025-07-23 17:00:27,903 - utils - INFO - [2025-07-23 17:00:27] 机器狗控制API关闭
2025-07-23 17:00:27,903 - utils - INFO - [2025-07-23 17:00:27] 没有活跃的机器狗连接
2025-07-23 17:00:29,066 - utils - INFO - [2025-07-23 17:00:29] 机器狗控制API启动
2025-07-23 17:00:45,994 - utils - INFO - [2025-07-23 17:00:45] 机器狗控制API关闭
2025-07-23 17:00:45,994 - utils - INFO - [2025-07-23 17:00:45] 没有活跃的机器狗连接
2025-07-23 17:00:47,014 - utils - INFO - [2025-07-23 17:00:47] 机器狗控制API启动
2025-07-23 17:00:59,343 - utils - INFO - [2025-07-23 17:00:59] 机器狗控制API关闭
2025-07-23 17:00:59,343 - utils - INFO - [2025-07-23 17:00:59] 没有活跃的机器狗连接
2025-07-23 17:01:00,361 - utils - INFO - [2025-07-23 17:01:00] 机器狗控制API启动
2025-07-23 17:01:37,987 - utils - INFO - [2025-07-23 17:01:37] 机器狗控制API关闭
2025-07-23 17:01:37,987 - utils - INFO - [2025-07-23 17:01:37] 没有活跃的机器狗连接
2025-07-23 17:01:38,975 - utils - INFO - [2025-07-23 17:01:38] 机器狗控制API启动
2025-07-23 17:02:15,260 - utils - INFO - [2025-07-23 17:02:15] 收到连接请求: ************
2025-07-23 17:02:15,260 - utils - INFO - [2025-07-23 17:02:15] 开始连接机器狗: ************
2025-07-23 17:02:15,260 - utils - INFO - [2025-07-23 17:02:15] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 17:02:15,292 - aioice.ice - INFO - Connection(2) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,293 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,293 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,294 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,294 - aioice.ice - INFO - Connection(0) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,294 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,295 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,295 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,296 - aioice.ice - INFO - Connection(1) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,296 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,296 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,296 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,296 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,297 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:15,297 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:02:20,313 - utils - INFO - [2025-07-23 17:02:20] WebRTC Offer已创建
2025-07-23 17:02:20,322 - utils - INFO - [2025-07-23 17:02:20] 发送WebRTC offer到机器狗: ************
2025-07-23 17:02:20,405 - utils - ERROR - [2025-07-23 17:02:20] 信令初始化失败: Server disconnected
2025-07-23 17:02:20,406 - utils - ERROR - [2025-07-23 17:02:20] 连接机器狗失败: ************
2025-07-23 17:02:20,406 - utils - ERROR - [2025-07-23 17:02:20] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 17:07:03,719 - utils - INFO - [2025-07-23 17:07:03] 机器狗控制API关闭
2025-07-23 17:07:03,719 - utils - INFO - [2025-07-23 17:07:03] 没有活跃的机器狗连接
2025-07-23 17:07:04,997 - utils - INFO - [2025-07-23 17:07:04] 机器狗控制API启动
2025-07-23 17:08:36,090 - utils - INFO - [2025-07-23 17:08:36] 机器狗控制API关闭
2025-07-23 17:08:36,090 - utils - INFO - [2025-07-23 17:08:36] 没有活跃的机器狗连接
2025-07-23 17:08:37,221 - utils - INFO - [2025-07-23 17:08:37] 机器狗控制API启动
2025-07-23 17:10:04,781 - utils - INFO - [2025-07-23 17:10:04] 机器狗控制API关闭
2025-07-23 17:10:04,781 - utils - INFO - [2025-07-23 17:10:04] 没有活跃的机器狗连接
2025-07-23 17:10:05,893 - utils - INFO - [2025-07-23 17:10:05] 机器狗控制API启动
2025-07-23 17:10:51,418 - utils - INFO - [2025-07-23 17:10:51] 机器狗控制API关闭
2025-07-23 17:10:51,418 - utils - INFO - [2025-07-23 17:10:51] 没有活跃的机器狗连接
2025-07-23 17:10:52,482 - utils - INFO - [2025-07-23 17:10:52] 机器狗控制API启动
2025-07-23 17:11:48,203 - utils - INFO - [2025-07-23 17:11:48] 机器狗控制API关闭
2025-07-23 17:11:48,203 - utils - INFO - [2025-07-23 17:11:48] 没有活跃的机器狗连接
2025-07-23 17:11:49,358 - utils - INFO - [2025-07-23 17:11:49] 机器狗控制API启动
2025-07-23 17:12:59,794 - utils - INFO - [2025-07-23 17:12:59] 机器狗控制API关闭
2025-07-23 17:12:59,795 - utils - INFO - [2025-07-23 17:12:59] 没有活跃的机器狗连接
2025-07-23 17:13:00,908 - utils - INFO - [2025-07-23 17:13:00] 机器狗控制API启动
2025-07-23 17:16:50,006 - utils - INFO - [2025-07-23 17:16:50] 机器狗控制API关闭
2025-07-23 17:16:50,006 - utils - INFO - [2025-07-23 17:16:50] 没有活跃的机器狗连接
2025-07-23 17:16:51,145 - utils - INFO - [2025-07-23 17:16:51] 机器狗控制API启动
2025-07-23 17:17:50,127 - utils - INFO - [2025-07-23 17:17:50] 机器狗控制API关闭
2025-07-23 17:17:50,128 - utils - INFO - [2025-07-23 17:17:50] 没有活跃的机器狗连接
2025-07-23 17:17:51,264 - utils - INFO - [2025-07-23 17:17:51] 机器狗控制API启动
2025-07-23 17:18:39,469 - utils - INFO - [2025-07-23 17:18:39] 机器狗控制API关闭
2025-07-23 17:18:39,470 - utils - INFO - [2025-07-23 17:18:39] 没有活跃的机器狗连接
2025-07-23 17:18:41,708 - utils - INFO - [2025-07-23 17:18:41] 机器狗控制API启动
2025-07-23 17:19:31,087 - utils - INFO - [2025-07-23 17:19:31] 机器狗控制API关闭
2025-07-23 17:19:31,088 - utils - INFO - [2025-07-23 17:19:31] 没有活跃的机器狗连接
2025-07-23 17:19:32,190 - utils - INFO - [2025-07-23 17:19:32] 机器狗控制API启动
2025-07-23 17:24:49,076 - utils - INFO - [2025-07-23 17:24:49] 机器狗控制API关闭
2025-07-23 17:24:49,076 - utils - INFO - [2025-07-23 17:24:49] 没有活跃的机器狗连接
2025-07-23 17:24:50,186 - utils - INFO - [2025-07-23 17:24:50] 机器狗控制API启动
2025-07-23 17:26:28,304 - utils - INFO - [2025-07-23 17:26:28] 机器狗控制API关闭
2025-07-23 17:26:28,304 - utils - INFO - [2025-07-23 17:26:28] 没有活跃的机器狗连接
2025-07-23 17:26:29,357 - utils - INFO - [2025-07-23 17:26:29] 机器狗控制API启动
2025-07-23 17:26:53,273 - utils - INFO - [2025-07-23 17:26:53] 机器狗控制API关闭
2025-07-23 17:26:53,273 - utils - INFO - [2025-07-23 17:26:53] 没有活跃的机器狗连接
2025-07-23 17:26:54,356 - utils - INFO - [2025-07-23 17:26:54] 机器狗控制API启动
2025-07-23 17:27:22,746 - utils - INFO - [2025-07-23 17:27:22] 机器狗控制API关闭
2025-07-23 17:27:22,747 - utils - INFO - [2025-07-23 17:27:22] 没有活跃的机器狗连接
2025-07-23 17:27:23,904 - utils - INFO - [2025-07-23 17:27:23] 机器狗控制API启动
2025-07-23 17:27:41,796 - utils - INFO - [2025-07-23 17:27:41] 机器狗控制API关闭
2025-07-23 17:27:41,796 - utils - INFO - [2025-07-23 17:27:41] 没有活跃的机器狗连接
2025-07-23 17:27:42,830 - utils - INFO - [2025-07-23 17:27:42] 机器狗控制API启动
2025-07-23 17:28:03,672 - utils - INFO - [2025-07-23 17:28:03] 机器狗控制API关闭
2025-07-23 17:28:03,672 - utils - INFO - [2025-07-23 17:28:03] 没有活跃的机器狗连接
2025-07-23 17:28:04,830 - utils - INFO - [2025-07-23 17:28:04] 机器狗控制API启动
2025-07-23 17:30:34,290 - utils - INFO - [2025-07-23 17:30:34] 收到连接请求: ************
2025-07-23 17:30:34,290 - utils - INFO - [2025-07-23 17:30:34] 开始连接机器狗: ************
2025-07-23 17:30:34,297 - utils - INFO - [2025-07-23 17:30:34] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 17:30:34,298 - utils - INFO - [2025-07-23 17:30:34] 开始连接机器狗...
2025-07-23 17:30:34,318 - aioice.ice - INFO - Connection(0) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,319 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,319 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,319 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,320 - aioice.ice - INFO - Connection(2) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,320 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,320 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,320 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,320 - aioice.ice - INFO - Connection(1) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,321 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,321 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,321 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,321 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,322 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:34,322 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:30:39,332 - utils - INFO - [2025-07-23 17:30:39] WebRTC Offer已创建
2025-07-23 17:30:39,741 - utils - INFO - [2025-07-23 17:30:39] 连接状态: connecting
2025-07-23 17:30:39,741 - utils - INFO - [2025-07-23 17:30:39] 连接状态变化: connecting
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 接收到轨道: audio
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 收到音频轨道
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 接收到轨道: video
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 收到视频轨道
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 机器狗连接成功
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 心跳已启动
2025-07-23 17:30:39,743 - utils - INFO - [2025-07-23 17:30:39] 成功连接到机器狗: ************
2025-07-23 17:30:39,769 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60821) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,801 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,826 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) failed : source address mismatch
2025-07-23 17:30:39,826 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,826 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,840 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) failed : source address mismatch
2025-07-23 17:30:39,840 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,840 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,860 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) failed : source address mismatch
2025-07-23 17:30:39,860 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,860 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,876 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) failed : source address mismatch
2025-07-23 17:30:39,876 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,877 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,899 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) failed : source address mismatch
2025-07-23 17:30:39,900 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 35777)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,900 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60821) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,926 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,939 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) failed : source address mismatch
2025-07-23 17:30:39,940 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,940 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,959 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) failed : source address mismatch
2025-07-23 17:30:39,960 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,960 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,976 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) failed : source address mismatch
2025-07-23 17:30:39,976 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:39,976 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:39,979 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) failed : source address mismatch
2025-07-23 17:30:39,979 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,004 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,019 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) failed : source address mismatch
2025-07-23 17:30:40,019 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 35009)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,020 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60821) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,040 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,065 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,068 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) failed : source address mismatch
2025-07-23 17:30:40,068 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,069 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) failed : source address mismatch
2025-07-23 17:30:40,069 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,080 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,085 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) failed : source address mismatch
2025-07-23 17:30:40,085 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,096 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,100 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) failed : source address mismatch
2025-07-23 17:30:40,100 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,127 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,149 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) failed : source address mismatch
2025-07-23 17:30:40,150 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 37198)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,150 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60821) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,174 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,189 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) failed : source address mismatch
2025-07-23 17:30:40,190 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,190 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,209 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) failed : source address mismatch
2025-07-23 17:30:40,210 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,210 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,226 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) failed : source address mismatch
2025-07-23 17:30:40,226 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,227 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,249 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) failed : source address mismatch
2025-07-23 17:30:40,250 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,250 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,266 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) failed : source address mismatch
2025-07-23 17:30:40,266 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 33990)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,267 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60821) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,290 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,313 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,316 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) failed : source address mismatch
2025-07-23 17:30:40,316 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60833) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,316 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) failed : source address mismatch
2025-07-23 17:30:40,316 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60836) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,330 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,350 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) failed : source address mismatch
2025-07-23 17:30:40,350 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60839) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,351 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,370 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) failed : source address mismatch
2025-07-23 17:30:40,370 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60842) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,371 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,376 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) failed : source address mismatch
2025-07-23 17:30:40,377 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60845) -> ('FD02:0A72:A560:9600:3490:86BD:C339:2331', 58405)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:30:40,407 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 60824) -> ('************', 38897)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,430 - aioice.ice - INFO - Connection(1) Check CandidatePair(('*************', 60827) -> ('************', 38897)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,454 - aioice.ice - INFO - Connection(1) Check CandidatePair(('*************', 60830) -> ('************', 38897)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,486 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 60848) -> ('************', 38897)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:30:40,489 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 60848) -> ('************', 38897)) State.IN_PROGRESS -> State.SUCCEEDED
2025-07-23 17:30:40,490 - aioice.ice - INFO - Connection(1) ICE completed
2025-07-23 17:30:56,363 - utils - ERROR - [2025-07-23 17:30:56] 机器狗未连接
2025-07-23 17:30:56,363 - utils - ERROR - [2025-07-23 17:30:56] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:57,863 - utils - ERROR - [2025-07-23 17:30:57] 机器狗未连接
2025-07-23 17:30:57,863 - utils - ERROR - [2025-07-23 17:30:57] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:58,317 - utils - ERROR - [2025-07-23 17:30:58] 机器狗未连接
2025-07-23 17:30:58,318 - utils - ERROR - [2025-07-23 17:30:58] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:58,699 - utils - ERROR - [2025-07-23 17:30:58] 机器狗未连接
2025-07-23 17:30:58,700 - utils - ERROR - [2025-07-23 17:30:58] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:59,086 - utils - ERROR - [2025-07-23 17:30:59] 机器狗未连接
2025-07-23 17:30:59,087 - utils - ERROR - [2025-07-23 17:30:59] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:59,447 - utils - ERROR - [2025-07-23 17:30:59] 机器狗未连接
2025-07-23 17:30:59,447 - utils - ERROR - [2025-07-23 17:30:59] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:59,660 - utils - ERROR - [2025-07-23 17:30:59] 机器狗未连接
2025-07-23 17:30:59,660 - utils - ERROR - [2025-07-23 17:30:59] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:59,800 - utils - ERROR - [2025-07-23 17:30:59] 机器狗未连接
2025-07-23 17:30:59,801 - utils - ERROR - [2025-07-23 17:30:59] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:30:59,935 - utils - ERROR - [2025-07-23 17:30:59] 机器狗未连接
2025-07-23 17:30:59,935 - utils - ERROR - [2025-07-23 17:30:59] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:31:00,073 - utils - ERROR - [2025-07-23 17:31:00] 机器狗未连接
2025-07-23 17:31:00,073 - utils - ERROR - [2025-07-23 17:31:00] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:31:00,206 - utils - ERROR - [2025-07-23 17:31:00] 机器狗未连接
2025-07-23 17:31:00,206 - utils - ERROR - [2025-07-23 17:31:00] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:31:00,345 - utils - ERROR - [2025-07-23 17:31:00] 机器狗未连接
2025-07-23 17:31:00,346 - utils - ERROR - [2025-07-23 17:31:00] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:31:00,500 - utils - ERROR - [2025-07-23 17:31:00] 机器狗未连接
2025-07-23 17:31:00,501 - utils - ERROR - [2025-07-23 17:31:00] 移动命令请求处理失败: 400: 移动命令发送失败
2025-07-23 17:31:20,416 - aioice.ice - INFO - Connection(1) Consent to send expired
2025-07-23 17:31:20,416 - utils - INFO - [2025-07-23 17:31:20] 连接状态: failed
2025-07-23 17:31:20,416 - utils - INFO - [2025-07-23 17:31:20] 连接状态变化: failed
2025-07-23 17:33:09,036 - utils - INFO - [2025-07-23 17:33:09] 机器狗控制API关闭
2025-07-23 17:33:09,036 - utils - INFO - [2025-07-23 17:33:09] 连接状态: closed
2025-07-23 17:33:09,037 - utils - INFO - [2025-07-23 17:33:09] 连接状态变化: closed
2025-07-23 17:33:09,037 - utils - INFO - [2025-07-23 17:33:09] WebRTC连接已断开
2025-07-23 17:33:09,037 - utils - INFO - [2025-07-23 17:33:09] 机器狗连接已断开
2025-07-23 17:33:10,230 - utils - INFO - [2025-07-23 17:33:10] 机器狗控制API启动
2025-07-23 17:34:13,990 - utils - INFO - [2025-07-23 17:34:13] 机器狗控制API关闭
2025-07-23 17:34:13,990 - utils - INFO - [2025-07-23 17:34:13] 没有活跃的机器狗连接
2025-07-23 17:34:15,107 - utils - INFO - [2025-07-23 17:34:15] 机器狗控制API启动
2025-07-23 17:34:51,794 - utils - INFO - [2025-07-23 17:34:51] 机器狗控制API关闭
2025-07-23 17:34:51,794 - utils - INFO - [2025-07-23 17:34:51] 没有活跃的机器狗连接
2025-07-23 17:34:52,844 - utils - INFO - [2025-07-23 17:34:52] 机器狗控制API启动
2025-07-23 17:35:41,223 - utils - INFO - [2025-07-23 17:35:41] 机器狗控制API关闭
2025-07-23 17:35:41,223 - utils - INFO - [2025-07-23 17:35:41] 没有活跃的机器狗连接
2025-07-23 17:35:42,289 - utils - INFO - [2025-07-23 17:35:42] 机器狗控制API启动
2025-07-23 17:36:24,103 - utils - INFO - [2025-07-23 17:36:24] 机器狗控制API关闭
2025-07-23 17:36:24,103 - utils - INFO - [2025-07-23 17:36:24] 没有活跃的机器狗连接
2025-07-23 17:36:25,196 - utils - INFO - [2025-07-23 17:36:25] 机器狗控制API启动
2025-07-23 17:39:38,068 - utils - INFO - [2025-07-23 17:39:38] 机器狗控制API关闭
2025-07-23 17:39:38,068 - utils - INFO - [2025-07-23 17:39:38] 没有活跃的机器狗连接
2025-07-23 17:39:39,168 - utils - INFO - [2025-07-23 17:39:39] 机器狗控制API启动
2025-07-23 17:40:01,318 - utils - INFO - [2025-07-23 17:40:01] 机器狗控制API关闭
2025-07-23 17:40:01,319 - utils - INFO - [2025-07-23 17:40:01] 没有活跃的机器狗连接
2025-07-23 17:40:02,377 - utils - INFO - [2025-07-23 17:40:02] 机器狗控制API启动
2025-07-23 17:40:33,986 - utils - INFO - [2025-07-23 17:40:33] 收到连接请求: ************
2025-07-23 17:40:33,986 - utils - INFO - [2025-07-23 17:40:33] 开始连接机器狗: ************
2025-07-23 17:40:33,992 - utils - INFO - [2025-07-23 17:40:33] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 17:40:33,992 - utils - INFO - [2025-07-23 17:40:33] 开始连接机器狗...
2025-07-23 17:40:34,013 - aioice.ice - INFO - Connection(1) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,013 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,014 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,014 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,014 - aioice.ice - INFO - Connection(0) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,014 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,016 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,016 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,016 - aioice.ice - INFO - Connection(2) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,017 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,017 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,017 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,018 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,018 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:34,018 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 17:40:39,015 - utils - INFO - [2025-07-23 17:40:39] WebRTC Offer已创建
2025-07-23 17:40:39,303 - utils - INFO - [2025-07-23 17:40:39] 连接状态: connecting
2025-07-23 17:40:39,304 - utils - INFO - [2025-07-23 17:40:39] 连接状态变化: connecting
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 接收到轨道: audio
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 收到音频轨道
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 接收到轨道: video
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 收到视频轨道
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 机器狗连接成功
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 心跳已启动
2025-07-23 17:40:39,306 - utils - INFO - [2025-07-23 17:40:39] 成功连接到机器狗: ************
2025-07-23 17:40:39,327 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60662) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,358 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,372 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) failed : source address mismatch
2025-07-23 17:40:39,372 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,372 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,392 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) failed : source address mismatch
2025-07-23 17:40:39,392 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,393 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,400 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) failed : source address mismatch
2025-07-23 17:40:39,400 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,400 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,412 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) failed : source address mismatch
2025-07-23 17:40:39,412 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,435 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,450 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) failed : source address mismatch
2025-07-23 17:40:39,450 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 55764)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,450 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60662) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,482 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,500 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) failed : source address mismatch
2025-07-23 17:40:39,500 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,501 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,522 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) failed : source address mismatch
2025-07-23 17:40:39,522 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,522 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,529 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,532 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) failed : source address mismatch
2025-07-23 17:40:39,532 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,532 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) failed : source address mismatch
2025-07-23 17:40:39,532 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,560 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,582 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) failed : source address mismatch
2025-07-23 17:40:39,582 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 49100)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,583 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60662) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,602 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,622 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,622 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) failed : source address mismatch
2025-07-23 17:40:39,623 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,641 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) failed : source address mismatch
2025-07-23 17:40:39,642 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,642 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,650 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) failed : source address mismatch
2025-07-23 17:40:39,651 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,651 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,662 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) failed : source address mismatch
2025-07-23 17:40:39,663 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,684 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,701 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) failed : source address mismatch
2025-07-23 17:40:39,701 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 51738)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,701 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60662) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,729 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,751 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) failed : source address mismatch
2025-07-23 17:40:39,751 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,752 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,772 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) failed : source address mismatch
2025-07-23 17:40:39,772 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,773 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,792 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) failed : source address mismatch
2025-07-23 17:40:39,792 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,793 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,800 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) failed : source address mismatch
2025-07-23 17:40:39,801 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,823 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,832 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) failed : source address mismatch
2025-07-23 17:40:39,833 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 55174)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,851 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 60662) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,885 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,901 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,904 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) failed : source address mismatch
2025-07-23 17:40:39,904 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 60674) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,905 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) failed : source address mismatch
2025-07-23 17:40:39,905 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 60677) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,933 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,942 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) failed : source address mismatch
2025-07-23 17:40:39,942 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 60680) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,962 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:39,982 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) failed : source address mismatch
2025-07-23 17:40:39,982 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:d414:30cd:bf71:e52c', 60683) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:39,982 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:40,001 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) failed : source address mismatch
2025-07-23 17:40:40,002 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:d414:30cd:bf71:e52c', 60686) -> ('FD02:0A72:A560:9600:2C00:D1E6:63C8:72B8', 44811)) State.IN_PROGRESS -> State.FAILED
2025-07-23 17:40:40,002 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 60665) -> ('************', 54193)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:40,028 - aioice.ice - INFO - Connection(1) Check CandidatePair(('*************', 60668) -> ('************', 54193)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:40,052 - aioice.ice - INFO - Connection(1) Check CandidatePair(('*************', 60671) -> ('************', 54193)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:40,075 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 60689) -> ('************', 54193)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 17:40:40,092 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 60689) -> ('************', 54193)) State.IN_PROGRESS -> State.SUCCEEDED
2025-07-23 17:40:40,092 - aioice.ice - INFO - Connection(1) ICE completed
2025-07-23 17:40:45,693 - utils - INFO - [2025-07-23 17:40:45] 数据通道状态: connecting
2025-07-23 17:40:45,709 - utils - INFO - [2025-07-23 17:40:45] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:40:59,141 - utils - INFO - [2025-07-23 17:40:59] 数据通道状态: connecting
2025-07-23 17:40:59,141 - utils - INFO - [2025-07-23 17:40:59] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:40:59,597 - utils - INFO - [2025-07-23 17:40:59] 数据通道状态: connecting
2025-07-23 17:40:59,597 - utils - INFO - [2025-07-23 17:40:59] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:00,324 - utils - INFO - [2025-07-23 17:41:00] 数据通道状态: connecting
2025-07-23 17:41:00,324 - utils - INFO - [2025-07-23 17:41:00] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:00,743 - utils - INFO - [2025-07-23 17:41:00] 数据通道状态: connecting
2025-07-23 17:41:00,744 - utils - INFO - [2025-07-23 17:41:00] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:01,090 - utils - INFO - [2025-07-23 17:41:01] 数据通道状态: connecting
2025-07-23 17:41:01,090 - utils - INFO - [2025-07-23 17:41:01] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:01,243 - utils - INFO - [2025-07-23 17:41:01] 数据通道状态: connecting
2025-07-23 17:41:01,244 - utils - INFO - [2025-07-23 17:41:01] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:01,426 - utils - INFO - [2025-07-23 17:41:01] 数据通道状态: connecting
2025-07-23 17:41:01,426 - utils - INFO - [2025-07-23 17:41:01] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:01,608 - utils - INFO - [2025-07-23 17:41:01] 数据通道状态: connecting
2025-07-23 17:41:01,608 - utils - INFO - [2025-07-23 17:41:01] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:01,817 - utils - INFO - [2025-07-23 17:41:01] 数据通道状态: connecting
2025-07-23 17:41:01,817 - utils - INFO - [2025-07-23 17:41:01] 移动命令已发送: x=0.500, y=0.000, z=0.000
2025-07-23 17:41:18,860 - aioice.ice - INFO - Connection(1) Consent to send expired
2025-07-23 17:41:18,860 - utils - INFO - [2025-07-23 17:41:18] 连接状态: failed
2025-07-23 17:41:18,861 - utils - INFO - [2025-07-23 17:41:18] 连接状态变化: failed
2025-07-23 18:00:03,413 - utils - INFO - [2025-07-23 18:00:03] 机器狗控制API启动
2025-07-23 18:00:59,105 - utils - INFO - [2025-07-23 18:00:59] 收到连接请求: ************
2025-07-23 18:00:59,106 - utils - INFO - [2025-07-23 18:00:59] 开始连接机器狗: ************
2025-07-23 18:00:59,107 - utils - ERROR - [2025-07-23 18:00:59] 连接机器狗异常: 官方SDK不可用，请检查SDK安装
2025-07-23 18:00:59,107 - utils - ERROR - [2025-07-23 18:00:59] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 18:00:59,974 - utils - INFO - [2025-07-23 18:00:59] 收到连接请求: ************
2025-07-23 18:00:59,975 - utils - INFO - [2025-07-23 18:00:59] 开始连接机器狗: ************
2025-07-23 18:00:59,975 - utils - ERROR - [2025-07-23 18:00:59] 连接机器狗异常: 官方SDK不可用，请检查SDK安装
2025-07-23 18:00:59,976 - utils - ERROR - [2025-07-23 18:00:59] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 18:05:44,990 - utils - INFO - [2025-07-23 18:05:44] 机器狗控制API关闭
2025-07-23 18:05:44,991 - utils - INFO - [2025-07-23 18:05:44] 没有活跃的机器狗连接
2025-07-23 18:05:45,751 - utils - INFO - [2025-07-23 18:05:45] 机器狗控制API启动
2025-07-23 18:06:01,956 - utils - INFO - [2025-07-23 18:06:01] 机器狗控制API关闭
2025-07-23 18:06:01,956 - utils - INFO - [2025-07-23 18:06:01] 没有活跃的机器狗连接
2025-07-23 18:06:02,661 - utils - INFO - [2025-07-23 18:06:02] 机器狗控制API启动
2025-07-23 18:06:29,251 - utils - INFO - [2025-07-23 18:06:29] 机器狗控制API关闭
2025-07-23 18:06:29,251 - utils - INFO - [2025-07-23 18:06:29] 没有活跃的机器狗连接
2025-07-23 18:06:29,940 - utils - INFO - [2025-07-23 18:06:29] 机器狗控制API启动
2025-07-23 18:06:47,616 - utils - INFO - [2025-07-23 18:06:47] 机器狗控制API关闭
2025-07-23 18:06:47,616 - utils - INFO - [2025-07-23 18:06:47] 没有活跃的机器狗连接
2025-07-23 18:06:48,334 - utils - INFO - [2025-07-23 18:06:48] 机器狗控制API启动
2025-07-23 18:07:10,347 - utils - INFO - [2025-07-23 18:07:10] 机器狗控制API关闭
2025-07-23 18:07:10,347 - utils - INFO - [2025-07-23 18:07:10] 没有活跃的机器狗连接
2025-07-23 18:07:11,284 - utils - INFO - [2025-07-23 18:07:11] 机器狗控制API启动
2025-07-23 18:07:38,816 - utils - INFO - [2025-07-23 18:07:38] 机器狗控制API关闭
2025-07-23 18:07:38,816 - utils - INFO - [2025-07-23 18:07:38] 没有活跃的机器狗连接
2025-07-23 18:07:39,517 - utils - INFO - [2025-07-23 18:07:39] 机器狗控制API启动
2025-07-23 18:07:58,801 - utils - INFO - [2025-07-23 18:07:58] 机器狗控制API关闭
2025-07-23 18:07:58,801 - utils - INFO - [2025-07-23 18:07:58] 没有活跃的机器狗连接
2025-07-23 18:07:59,509 - utils - INFO - [2025-07-23 18:07:59] 机器狗控制API启动
2025-07-23 18:08:25,966 - utils - INFO - [2025-07-23 18:08:25] 机器狗控制API关闭
2025-07-23 18:08:25,966 - utils - INFO - [2025-07-23 18:08:25] 没有活跃的机器狗连接
2025-07-23 18:08:26,655 - utils - INFO - [2025-07-23 18:08:26] 机器狗控制API启动
2025-07-23 18:08:50,837 - utils - INFO - [2025-07-23 18:08:50] 机器狗控制API关闭
2025-07-23 18:08:50,837 - utils - INFO - [2025-07-23 18:08:50] 没有活跃的机器狗连接
2025-07-23 18:08:51,668 - utils - INFO - [2025-07-23 18:08:51] 机器狗控制API启动
2025-07-23 18:14:47,310 - utils - INFO - [2025-07-23 18:14:47] 收到连接请求: ************
2025-07-23 18:14:47,311 - utils - INFO - [2025-07-23 18:14:47] 开始连接机器狗: ************
2025-07-23 18:14:47,311 - utils - INFO - [2025-07-23 18:14:47] Go2SDKClient初始化 - IP: ************, 接口: wlan0
2025-07-23 18:14:47,312 - utils - INFO - [2025-07-23 18:14:47] 开始连接机器狗...
2025-07-23 18:14:51,411 - utils - ERROR - [2025-07-23 18:14:51] 连接失败: HTTPConnectionPool(host='************', port=8080): Max retries exceeded with url: /api/sport/request (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000029D581C2B40>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-23 18:14:51,412 - utils - ERROR - [2025-07-23 18:14:51] 连接机器狗失败: ************
2025-07-23 18:14:51,423 - utils - ERROR - [2025-07-23 18:14:51] 连接请求处理失败: 400: 连接机器狗失败
2025-07-23 18:16:25,924 - utils - INFO - [2025-07-23 18:16:25] 机器狗控制API关闭
2025-07-23 18:16:25,924 - utils - INFO - [2025-07-23 18:16:25] 没有活跃的机器狗连接
2025-07-23 18:16:26,626 - utils - INFO - [2025-07-23 18:16:26] 机器狗控制API启动
2025-07-23 18:16:47,248 - utils - INFO - [2025-07-23 18:16:47] 机器狗控制API关闭
2025-07-23 18:16:47,248 - utils - INFO - [2025-07-23 18:16:47] 没有活跃的机器狗连接
2025-07-23 18:16:47,962 - utils - INFO - [2025-07-23 18:16:47] 机器狗控制API启动
2025-07-23 18:17:23,264 - utils - INFO - [2025-07-23 18:17:23] 机器狗控制API关闭
2025-07-23 18:17:23,264 - utils - INFO - [2025-07-23 18:17:23] 没有活跃的机器狗连接
2025-07-23 18:17:24,025 - utils - INFO - [2025-07-23 18:17:24] 机器狗控制API启动
2025-07-23 18:17:50,863 - utils - INFO - [2025-07-23 18:17:50] 机器狗控制API关闭
2025-07-23 18:17:50,863 - utils - INFO - [2025-07-23 18:17:50] 没有活跃的机器狗连接
2025-07-23 18:17:51,669 - utils - INFO - [2025-07-23 18:17:51] 机器狗控制API启动
2025-07-23 18:18:10,820 - utils - INFO - [2025-07-23 18:18:10] 机器狗控制API关闭
2025-07-23 18:18:10,820 - utils - INFO - [2025-07-23 18:18:10] 没有活跃的机器狗连接
2025-07-23 18:18:12,006 - utils - INFO - [2025-07-23 18:18:12] 机器狗控制API启动
2025-07-23 18:18:38,781 - utils - INFO - [2025-07-23 18:18:38] 机器狗控制API启动
2025-07-23 18:19:03,579 - utils - INFO - [2025-07-23 18:19:03] 收到连接请求: ************
2025-07-23 18:19:03,580 - utils - INFO - [2025-07-23 18:19:03] 开始连接机器狗: ************
2025-07-23 18:19:03,588 - utils - INFO - [2025-07-23 18:19:03] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 18:19:03,588 - utils - INFO - [2025-07-23 18:19:03] 开始连接机器狗...
2025-07-23 18:19:03,624 - aioice.ice - INFO - Connection(1) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,625 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,626 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,626 - aioice.ice - INFO - Connection(1) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,627 - aioice.ice - INFO - Connection(0) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,627 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,628 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,628 - aioice.ice - INFO - Connection(0) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,628 - aioice.ice - INFO - Connection(2) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,629 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,629 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,630 - aioice.ice - INFO - Connection(2) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,631 - aioice.ice - INFO - Connection(1) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,632 - aioice.ice - INFO - Connection(0) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:03,634 - aioice.ice - INFO - Connection(2) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:08,645 - utils - INFO - [2025-07-23 18:19:08] WebRTC Offer已创建
2025-07-23 18:19:08,881 - utils - INFO - [2025-07-23 18:19:08] 连接状态: connecting
2025-07-23 18:19:08,882 - utils - INFO - [2025-07-23 18:19:08] 连接状态变化: connecting
2025-07-23 18:19:08,885 - utils - INFO - [2025-07-23 18:19:08] 接收到轨道: audio
2025-07-23 18:19:08,885 - utils - INFO - [2025-07-23 18:19:08] 收到音频轨道
2025-07-23 18:19:08,886 - utils - INFO - [2025-07-23 18:19:08] 接收到轨道: video
2025-07-23 18:19:08,886 - utils - INFO - [2025-07-23 18:19:08] 收到视频轨道
2025-07-23 18:19:08,886 - utils - INFO - [2025-07-23 18:19:08] 机器狗连接成功
2025-07-23 18:19:08,887 - utils - INFO - [2025-07-23 18:19:08] 心跳已启动
2025-07-23 18:19:08,887 - utils - INFO - [2025-07-23 18:19:08] 成功连接到机器狗: ************
2025-07-23 18:19:08,911 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 58827) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:08,943 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:08,963 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) failed : source address mismatch
2025-07-23 18:19:08,963 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:08,964 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:08,969 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) failed : source address mismatch
2025-07-23 18:19:08,969 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:08,990 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,009 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) failed : source address mismatch
2025-07-23 18:19:09,010 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,010 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,016 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) failed : source address mismatch
2025-07-23 18:19:09,016 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,052 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,059 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) failed : source address mismatch
2025-07-23 18:19:09,060 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 33910)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,060 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 58827) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,079 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,114 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,116 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) failed : source address mismatch
2025-07-23 18:19:09,117 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,119 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) failed : source address mismatch
2025-07-23 18:19:09,120 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,120 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,140 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) failed : source address mismatch
2025-07-23 18:19:09,141 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,141 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,159 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) failed : source address mismatch
2025-07-23 18:19:09,159 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,160 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,166 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) failed : source address mismatch
2025-07-23 18:19:09,168 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48687)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,169 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 58827) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,192 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,207 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,212 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) failed : source address mismatch
2025-07-23 18:19:09,212 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,213 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,214 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) failed : source address mismatch
2025-07-23 18:19:09,217 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,218 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) failed : source address mismatch
2025-07-23 18:19:09,218 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,254 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,259 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) failed : source address mismatch
2025-07-23 18:19:09,260 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,260 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,267 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) failed : source address mismatch
2025-07-23 18:19:09,267 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 54030)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,301 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 58827) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,319 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,331 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,360 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) failed : source address mismatch
2025-07-23 18:19:09,371 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,372 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,376 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) failed : source address mismatch
2025-07-23 18:19:09,377 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,408 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,416 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) failed : source address mismatch
2025-07-23 18:19:09,417 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,417 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,429 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) failed : source address mismatch
2025-07-23 18:19:09,429 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 53211)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,430 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fdfd::1a3e:515c', 58827) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,455 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,467 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,470 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) failed : source address mismatch
2025-07-23 18:19:09,470 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 58839) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,471 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) failed : source address mismatch
2025-07-23 18:19:09,471 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 58842) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,501 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,509 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) failed : source address mismatch
2025-07-23 18:19:09,509 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 58845) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,510 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,517 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) failed : source address mismatch
2025-07-23 18:19:09,517 - aioice.ice - INFO - Connection(1) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 58848) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,547 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,559 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) failed : source address mismatch
2025-07-23 18:19:09,559 - aioice.ice - INFO - Connection(1) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 58851) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 59756)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:09,561 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 58830) -> ('************', 37741)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,580 - aioice.ice - INFO - Connection(1) Check CandidatePair(('*************', 58833) -> ('************', 37741)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,609 - aioice.ice - INFO - Connection(1) Check CandidatePair(('*************', 58836) -> ('************', 37741)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,629 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 58854) -> ('************', 37741)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:09,649 - aioice.ice - INFO - Connection(1) Check CandidatePair(('***********', 58854) -> ('************', 37741)) State.IN_PROGRESS -> State.SUCCEEDED
2025-07-23 18:19:09,649 - aioice.ice - INFO - Connection(1) ICE completed
2025-07-23 18:19:20,964 - utils - INFO - [2025-07-23 18:19:20] 收到连接请求: ************
2025-07-23 18:19:20,964 - utils - INFO - [2025-07-23 18:19:20] 开始连接机器狗: ************
2025-07-23 18:19:20,965 - utils - INFO - [2025-07-23 18:19:20] 连接状态: closed
2025-07-23 18:19:20,965 - utils - INFO - [2025-07-23 18:19:20] 连接状态变化: closed
2025-07-23 18:19:20,965 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-28' coro=<RTCPeerConnection.__connect() done, defined at D:\python\Lib\site-packages\aiortc\rtcpeerconnection.py:1048> exception=InvalidStateError('RTCIceTransport is closed')>
Traceback (most recent call last):
  File "D:\python\Lib\site-packages\aiortc\rtcpeerconnection.py", line 1056, in __connect
    await iceTransport.start(self.__remoteIce[transceiver])
  File "D:\python\Lib\site-packages\aiortc\rtcicetransport.py", line 324, in start
    raise InvalidStateError("RTCIceTransport is closed")
aiortc.exceptions.InvalidStateError: RTCIceTransport is closed
2025-07-23 18:19:20,967 - utils - INFO - [2025-07-23 18:19:20] WebRTC连接已断开
2025-07-23 18:19:20,967 - utils - INFO - [2025-07-23 18:19:20] 机器狗连接已断开
2025-07-23 18:19:20,968 - utils - INFO - [2025-07-23 18:19:20] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 18:19:20,969 - utils - INFO - [2025-07-23 18:19:20] 开始连接机器狗...
2025-07-23 18:19:20,988 - aioice.ice - INFO - Connection(4) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,988 - aioice.ice - INFO - Connection(4) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,988 - aioice.ice - INFO - Connection(4) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,989 - aioice.ice - INFO - Connection(4) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,989 - aioice.ice - INFO - Connection(5) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,989 - aioice.ice - INFO - Connection(5) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,990 - aioice.ice - INFO - Connection(5) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,990 - aioice.ice - INFO - Connection(5) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,990 - aioice.ice - INFO - Connection(3) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,990 - aioice.ice - INFO - Connection(3) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,991 - aioice.ice - INFO - Connection(3) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,991 - aioice.ice - INFO - Connection(3) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,991 - aioice.ice - INFO - Connection(4) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,991 - aioice.ice - INFO - Connection(5) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:20,992 - aioice.ice - INFO - Connection(3) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:25,999 - utils - INFO - [2025-07-23 18:19:25] WebRTC Offer已创建
2025-07-23 18:19:26,250 - utils - INFO - [2025-07-23 18:19:26] 连接状态: connecting
2025-07-23 18:19:26,250 - utils - INFO - [2025-07-23 18:19:26] 连接状态变化: connecting
2025-07-23 18:19:26,253 - utils - INFO - [2025-07-23 18:19:26] 接收到轨道: audio
2025-07-23 18:19:26,253 - utils - INFO - [2025-07-23 18:19:26] 收到音频轨道
2025-07-23 18:19:26,253 - utils - INFO - [2025-07-23 18:19:26] 接收到轨道: video
2025-07-23 18:19:26,253 - utils - INFO - [2025-07-23 18:19:26] 收到视频轨道
2025-07-23 18:19:26,253 - utils - INFO - [2025-07-23 18:19:26] 机器狗连接成功
2025-07-23 18:19:26,253 - utils - INFO - [2025-07-23 18:19:26] 心跳已启动
2025-07-23 18:19:26,254 - utils - INFO - [2025-07-23 18:19:26] 成功连接到机器狗: ************
2025-07-23 18:19:26,279 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fdfd::1a3e:515c', 62528) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,310 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,329 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) failed : source address mismatch
2025-07-23 18:19:26,329 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,329 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,342 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) failed : source address mismatch
2025-07-23 18:19:26,342 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,342 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,349 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) failed : source address mismatch
2025-07-23 18:19:26,349 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,372 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,379 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) failed : source address mismatch
2025-07-23 18:19:26,379 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,400 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,420 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) failed : source address mismatch
2025-07-23 18:19:26,420 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 47234)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,420 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fdfd::1a3e:515c', 62528) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,449 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,480 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,511 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,543 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,546 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) failed : source address mismatch
2025-07-23 18:19:26,546 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,547 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) failed : source address mismatch
2025-07-23 18:19:26,547 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,547 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) failed : source address mismatch
2025-07-23 18:19:26,547 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,549 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) failed : source address mismatch
2025-07-23 18:19:26,549 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,573 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,605 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fdfd::1a3e:515c', 62528) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,635 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,667 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,699 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,701 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) failed : source address mismatch
2025-07-23 18:19:26,701 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 33914)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,703 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) failed : source address mismatch
2025-07-23 18:19:26,704 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,704 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) failed : source address mismatch
2025-07-23 18:19:26,704 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,719 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) failed : source address mismatch
2025-07-23 18:19:26,719 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,720 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,745 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,777 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fdfd::1a3e:515c', 62528) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,778 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) failed : source address mismatch
2025-07-23 18:19:26,778 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,796 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) failed : source address mismatch
2025-07-23 18:19:26,796 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 49064)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,796 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,825 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,855 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,860 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) failed : source address mismatch
2025-07-23 18:19:26,860 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,863 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) failed : source address mismatch
2025-07-23 18:19:26,864 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,864 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) failed : source address mismatch
2025-07-23 18:19:26,864 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,886 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,917 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,945 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) failed : source address mismatch
2025-07-23 18:19:26,946 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,946 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fdfd::1a3e:515c', 62528) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:26,948 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) failed : source address mismatch
2025-07-23 18:19:26,948 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 40312)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:26,978 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,010 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,039 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) failed : source address mismatch
2025-07-23 18:19:27,039 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 62540) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:27,039 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,042 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) failed : source address mismatch
2025-07-23 18:19:27,042 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 62543) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:27,043 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) failed : source address mismatch
2025-07-23 18:19:27,043 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 62546) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:27,059 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,088 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,095 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) failed : source address mismatch
2025-07-23 18:19:27,095 - aioice.ice - INFO - Connection(4) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 62549) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:27,100 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) failed : source address mismatch
2025-07-23 18:19:27,100 - aioice.ice - INFO - Connection(4) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 62552) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 33524)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:27,120 - aioice.ice - INFO - Connection(4) Check CandidatePair(('***********', 62531) -> ('************', 32898)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,151 - aioice.ice - INFO - Connection(4) Check CandidatePair(('*************', 62534) -> ('************', 32898)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,169 - aioice.ice - INFO - Connection(4) Check CandidatePair(('*************', 62537) -> ('************', 32898)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,198 - aioice.ice - INFO - Connection(4) Check CandidatePair(('***********', 62555) -> ('************', 32898)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:27,317 - aioice.ice - INFO - Connection(4) Check CandidatePair(('***********', 62555) -> ('************', 32898)) State.IN_PROGRESS -> State.SUCCEEDED
2025-07-23 18:19:27,318 - aioice.ice - INFO - Connection(4) ICE completed
2025-07-23 18:19:29,536 - utils - INFO - [2025-07-23 18:19:29] 机器狗控制API关闭
2025-07-23 18:19:29,536 - utils - INFO - [2025-07-23 18:19:29] 没有活跃的机器狗连接
2025-07-23 18:19:33,163 - utils - INFO - [2025-07-23 18:19:33] 机器狗控制API启动
2025-07-23 18:19:38,161 - utils - INFO - [2025-07-23 18:19:38] 收到连接请求: ************
2025-07-23 18:19:38,162 - utils - INFO - [2025-07-23 18:19:38] 开始连接机器狗: ************
2025-07-23 18:19:38,162 - utils - INFO - [2025-07-23 18:19:38] 连接状态: closed
2025-07-23 18:19:38,162 - utils - INFO - [2025-07-23 18:19:38] 连接状态变化: closed
2025-07-23 18:19:38,163 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-97' coro=<RTCPeerConnection.__connect() done, defined at D:\python\Lib\site-packages\aiortc\rtcpeerconnection.py:1048> exception=InvalidStateError('RTCIceTransport is closed')>
Traceback (most recent call last):
  File "D:\python\Lib\site-packages\aiortc\rtcpeerconnection.py", line 1056, in __connect
    await iceTransport.start(self.__remoteIce[transceiver])
  File "D:\python\Lib\site-packages\aiortc\rtcicetransport.py", line 324, in start
    raise InvalidStateError("RTCIceTransport is closed")
aiortc.exceptions.InvalidStateError: RTCIceTransport is closed
2025-07-23 18:19:38,165 - utils - INFO - [2025-07-23 18:19:38] WebRTC连接已断开
2025-07-23 18:19:38,165 - utils - INFO - [2025-07-23 18:19:38] 机器狗连接已断开
2025-07-23 18:19:38,168 - utils - INFO - [2025-07-23 18:19:38] Go2WebRTC实例已创建，机器狗IP: ************
2025-07-23 18:19:38,168 - utils - INFO - [2025-07-23 18:19:38] 开始连接机器狗...
2025-07-23 18:19:38,196 - aioice.ice - INFO - Connection(8) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,197 - aioice.ice - INFO - Connection(8) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,197 - aioice.ice - INFO - Connection(8) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,198 - aioice.ice - INFO - Connection(8) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,198 - aioice.ice - INFO - Connection(7) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,198 - aioice.ice - INFO - Connection(7) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,199 - aioice.ice - INFO - Connection(7) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,199 - aioice.ice - INFO - Connection(7) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,199 - aioice.ice - INFO - Connection(6) Could not bind to ************* - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,200 - aioice.ice - INFO - Connection(6) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,200 - aioice.ice - INFO - Connection(6) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,200 - aioice.ice - INFO - Connection(6) Could not bind to *************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,200 - aioice.ice - INFO - Connection(8) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,201 - aioice.ice - INFO - Connection(7) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:38,201 - aioice.ice - INFO - Connection(6) Could not bind to ************** - [WinError 10049] 在其上下文中，该请求的地址无效。
2025-07-23 18:19:43,209 - utils - INFO - [2025-07-23 18:19:43] WebRTC Offer已创建
2025-07-23 18:19:43,530 - utils - INFO - [2025-07-23 18:19:43] 连接状态: connecting
2025-07-23 18:19:43,533 - utils - INFO - [2025-07-23 18:19:43] 连接状态变化: connecting
2025-07-23 18:19:43,536 - utils - INFO - [2025-07-23 18:19:43] 接收到轨道: audio
2025-07-23 18:19:43,536 - utils - INFO - [2025-07-23 18:19:43] 收到音频轨道
2025-07-23 18:19:43,537 - utils - INFO - [2025-07-23 18:19:43] 接收到轨道: video
2025-07-23 18:19:43,537 - utils - INFO - [2025-07-23 18:19:43] 收到视频轨道
2025-07-23 18:19:43,537 - utils - INFO - [2025-07-23 18:19:43] 机器狗连接成功
2025-07-23 18:19:43,537 - utils - INFO - [2025-07-23 18:19:43] 心跳已启动
2025-07-23 18:19:43,539 - utils - INFO - [2025-07-23 18:19:43] 成功连接到机器狗: ************
2025-07-23 18:19:43,540 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fdfd::1a3e:515c', 51657) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,568 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,579 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) failed : source address mismatch
2025-07-23 18:19:43,580 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,580 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,599 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) failed : source address mismatch
2025-07-23 18:19:43,600 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,600 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,617 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) failed : source address mismatch
2025-07-23 18:19:43,618 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,618 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,639 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) failed : source address mismatch
2025-07-23 18:19:43,639 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,640 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,659 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) failed : source address mismatch
2025-07-23 18:19:43,659 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:801B:7EB8:E48A:2576', 43164)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,659 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fdfd::1a3e:515c', 51657) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,679 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,692 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,698 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) failed : source address mismatch
2025-07-23 18:19:43,700 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,700 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,701 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) failed : source address mismatch
2025-07-23 18:19:43,701 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,717 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) failed : source address mismatch
2025-07-23 18:19:43,717 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,718 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,738 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,738 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) failed : source address mismatch
2025-07-23 18:19:43,740 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,758 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) failed : source address mismatch
2025-07-23 18:19:43,760 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:C955:E7A2:9F99:F16F', 48203)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,760 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fdfd::1a3e:515c', 51657) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,778 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,798 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) failed : source address mismatch
2025-07-23 18:19:43,800 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,800 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,817 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) failed : source address mismatch
2025-07-23 18:19:43,818 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,820 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,839 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) failed : source address mismatch
2025-07-23 18:19:43,839 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,839 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,859 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) failed : source address mismatch
2025-07-23 18:19:43,859 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,859 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,867 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) failed : source address mismatch
2025-07-23 18:19:43,867 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('2408:8940:0051:3D3A:020A:72A5:6096:0004', 36156)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,868 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fdfd::1a3e:515c', 51657) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,894 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,899 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) failed : source address mismatch
2025-07-23 18:19:43,899 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,900 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,918 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) failed : source address mismatch
2025-07-23 18:19:43,918 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,918 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,939 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) failed : source address mismatch
2025-07-23 18:19:43,939 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,939 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,959 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) failed : source address mismatch
2025-07-23 18:19:43,959 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,960 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:43,967 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) failed : source address mismatch
2025-07-23 18:19:43,967 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('FD02:0A72:A560:9600:20A2:99CA:00C8:2F77', 48432)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:43,988 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fdfd::1a3e:515c', 51657) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,019 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,023 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) failed : source address mismatch
2025-07-23 18:19:44,024 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:20a:72a5:6096:5', 51669) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:44,034 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,050 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) failed : source address mismatch
2025-07-23 18:19:44,050 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:c84c:8bfa:bad1:c210', 51672) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:44,051 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,068 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) failed : source address mismatch
2025-07-23 18:19:44,068 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:6aa7:9b18:b2f8:8a57', 51675) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:44,069 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,089 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) failed : source address mismatch
2025-07-23 18:19:44,090 - aioice.ice - INFO - Connection(7) Check CandidatePair(('2408:8940:51:3d3a:71fd:170d:e8f2:2809', 51678) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:44,090 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,109 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) failed : source address mismatch
2025-07-23 18:19:44,110 - aioice.ice - INFO - Connection(7) Check CandidatePair(('fd02:a72:a560:9600:71fd:170d:e8f2:2809', 51681) -> ('FD02:0A72:A560:9600:6737:28E1:D7E1:294C', 49327)) State.IN_PROGRESS -> State.FAILED
2025-07-23 18:19:44,110 - aioice.ice - INFO - Connection(7) Check CandidatePair(('***********', 51660) -> ('************', 57753)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,130 - aioice.ice - INFO - Connection(7) Check CandidatePair(('*************', 51663) -> ('************', 57753)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,158 - aioice.ice - INFO - Connection(7) Check CandidatePair(('*************', 51666) -> ('************', 57753)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,180 - aioice.ice - INFO - Connection(7) Check CandidatePair(('***********', 51684) -> ('************', 57753)) State.FROZEN -> State.IN_PROGRESS
2025-07-23 18:19:44,199 - aioice.ice - INFO - Connection(7) Check CandidatePair(('***********', 51684) -> ('************', 57753)) State.IN_PROGRESS -> State.SUCCEEDED
2025-07-23 18:19:44,199 - aioice.ice - INFO - Connection(7) ICE completed
2025-07-23 18:20:17,561 - utils - INFO - [2025-07-23 18:20:17] 机器狗控制API关闭
2025-07-23 18:20:17,561 - utils - INFO - [2025-07-23 18:20:17] 没有活跃的机器狗连接
2025-07-23 18:20:23,720 - aioice.ice - INFO - Connection(7) Consent to send expired
2025-07-23 18:20:23,721 - utils - INFO - [2025-07-23 18:20:23] 连接状态: failed
2025-07-23 18:20:23,721 - utils - INFO - [2025-07-23 18:20:23] 连接状态变化: failed
