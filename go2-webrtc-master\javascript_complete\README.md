# go2-webrtc - WebRTC API or Unitree GO2 Robots

The `go2-webrtc` project provides a WebRTC API for Unitree GO2 Robots, enabling real-time communication and control over these robots through a web interface. This project simplifies the process of connecting to and controlling Unitree GO2 Robots by leveraging the WebRTC protocol for efficient, low-latency communication.

Go2's WebRTC API supports all models, including Go2 Air, Pro, and Edu versions.

This branch does not work with firmwares prior to 1.1.1. If you have an older firmware, use the previous commit from the repository.

## Features

- **WebRTC Integration**: Utilizes WebRTC to establish a real-time communication channel between the web client and the robot.
- **User Interface**: Includes a simple web interface for connecting to the robot, sending commands, and viewing the robot's video stream.
- **Command Execution**: Allows users to execute predefined commands on the robot, such as movement and action commands.
- **Persistent Settings**: Saves connection settings (token and robot IP) in the browser's localStorage for easier reconnection.

## Getting Started

To get started with `go2-webrtc`, clone this repository and serve the `index.html` file from the backend server (`server.py`) to a modern web browser. Ensure that your Unitree GO2 Robot is powered on and connected to the same network as your computer.

```
git clone https://github.com/tfoldi/go2-webrtc
cd go2-webrtc
pip install -r python/requirements.txt
cd javascript
python ./server.py
```

### Prerequisites

- A Unitree GO2 Robot accessible over the local network. All models supported.
- A web browser that supports WebRTC (e.g., Chrome, Firefox).

### Usage

1. Enter your security token and robot IP address in the web interface.
2. Click the "Connect" button to establish a connection to the robot.
3. Use the command input to send commands to the robot.
4. The video stream from the robot (if available) will be displayed in the web interface.

### Obtaining security token

Connecting to your device without a security token is possible and might allow a connection to be established. However, this method limits you to a single active connection at any time. To simultaneously use multiple clients, such as a WebRTC-based application and a phone app, a valid security token is necessary. This ensures secure, multi-client access to your device.

The easiest way is to sniff the traffic between the dog and your phone. Assuming that you have Linux or Mac:

1. Run `tinyproxy` or any other HTTP proxy on your computer
2. Set your computer's IP and port as HTTP proxy on your phone
3. Run wireshark or `ngrep` on your box sniffing port 8081 `like ngrep port 8081`.
4. Look for the token in the TCP stream after you connect your phone to the dog via the app

The token looks like this in the request payload:

```
{
    "token": "eyJ0eXAiOizI1NiJtlbiI[..]CI6MTcwODAxMzUwOX0.hiWOd9tNCIPzOOLNA",
    "sdp": "v=0\r\no=- ",
    "id": "STA_localNetwork",
    "type": "offer"
}
```
## Development

This project is structured around several key JavaScript files:

- `index.js`: Main script for handling UI interactions and storing settings.
- `go2webrtc.js`: Core WebRTC functionality for connecting to and communicating with the robot. Can be used standalone as an API wrapper.
- `utils.js`: Utility functions, including encryption helpers.
- `constants.js`: Defines constants and command codes for robot control.
- `server.py`: Python server used for CORS proxying

To contribute or modify the project, refer to these files for implementing additional features or improving the existing codebase. PRs are welcome.

## License

This project is licensed under the BSD 2-clause License - see the [LICENSE](https://github.com/tfoldi/go2-webrtc/blob/master/LICENSE) file for details.

# Unitree Go2 WebRTC Control Interface

## 功能特性

### 🎥 视频流
- 实时视频流接收
- 支持桌面和移动端查看
- 自适应视频分辨率

### 🎮 机器人控制
- WebRTC实时控制连接
- 多种运动模式支持
- 手柄控制支持
- 虚拟摇杆控制

### 🔊 音频功能
- **实时音频接收**: 接收机器人麦克风的音频流
- **音频可视化**: 实时音频频谱显示
- **音量控制**: 支持音量调节和静音
- **🎤 语音发送**: 录制语音并发送给机器人播放（主要功能）

## 🎤 语音发送功能使用说明

### 💡 快速开始
1. **连接机器狗**: 输入IP地址，点击连接，等待连接成功
2. **开始录音**: 在音频面板找到"语音发送"区域，点击绿色"开始录音"按钮
3. **授权麦克风**: 浏览器会请求麦克风权限，请点击"允许"
4. **说话录音**: 看到红色录音状态后开始说话，最长10秒
5. **停止发送**: 点击红色"停止录音"按钮，系统自动处理并发送给机器狗
6. **等待播放**: 机器狗扬声器会播放您录制的声音

### ✨ 主要特性
- **即录即发**: 录音完成立即发送，无需手动操作
- **高质量音频**: 16kHz采样率，专为语音优化
- **智能降噪**: 自动回声消除和噪音抑制
- **实时反馈**: 录音状态、音量显示、剩余时间提示
- **稳定传输**: 8KB分块传输，确保大音频文件稳定发送

### ⚠️ 注意事项
- 确保机器狗连接正常（连接状态显示"已连接"）
- 首次使用需要授权浏览器麦克风权限
- 建议录音时长控制在5-8秒内效果最佳
- 在安静环境录音可获得更好的播放效果

### 📋 详细说明
请查看 [语音发送功能使用说明.md](语音发送功能使用说明.md) 获取完整的技术文档和故障排除指南。

## 操控模式

### 桌面模式
- 键盘控制
- 鼠标操作
- 手柄支持

### 移动模式
- 虚拟摇杆控制
- 触屏优化
- 横屏全屏操作
- 速度调节滑块

## 系统要求

- 现代浏览器支持 (Chrome, Firefox, Safari, Edge)
- WebRTC 支持
- 稳定的网络连接
- **麦克风权限** (用于录音功能)

## 🚀 快速开始

1. **连接设备**: 输入机器狗IP地址，点击"连接"按钮
2. **等待连接**: 状态显示"已连接"后即可使用所有功能
3. **🎤 语音交流**: 在音频面板找到"语音发送"区域
   - 点击绿色"开始录音"按钮
   - 授权麦克风权限后开始说话
   - 点击红色"停止录音"按钮
   - 机器狗会播放您的声音！
4. **其他功能**: 选择控制模式进行移动控制或查看视频流

## 🛠️ 故障排除

### 🎤 语音发送问题
| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 无法录音 | 麦克风权限未授权 | 浏览器地址栏 → 权限 → 麦克风 → 允许 |
| 录音没声音 | 麦克风硬件问题 | 检查设备麦克风，观察音量条变化 |
| 发送失败 | 机器狗未连接 | 确保连接状态显示"已连接" |
| 机器狗不播放 | 音频传输错误 | 查看音频日志，重新录音尝试 |
| 音质差 | 环境噪音 | 安静环境录音，距离麦克风20-30cm |

### 🔗 连接问题
| 问题 | 解决方案 |
|------|----------|
| 无法连接 | 检查机器狗IP地址是否正确 |
| 连接断开 | 确保设备在同一WiFi网络 |
| 延迟高 | 使用有线网络连接 |

---
*更新日期: 2024年 - 新增麦克风录音功能*
