"""
  Generated by Eclipse Cyclone DDS idlc Python Backend
  Cyclone DDS IDL version: v0.11.0
  Module: nav_msgs.msg.dds_
  IDL file: OccupancyGrid_.idl

"""

from enum import auto
from typing import TYPE_CHECKING, Optional
from dataclasses import dataclass

import cyclonedds.idl as idl
import cyclonedds.idl.annotations as annotate
import cyclonedds.idl.types as types

# root module import for resolving types
# import nav_msgs

# if TYPE_CHECKING:
#      import std_msgs.msg.dds_



@dataclass
@annotate.final
@annotate.autoid("sequential")
class OccupancyGrid_(idl.IdlStruct, typename="nav_msgs.msg.dds_.OccupancyGrid_"):
    header: 'unitree_sdk2py.idl.std_msgs.msg.dds_.Header_'
    info: 'unitree_sdk2py.idl.nav_msgs.msg.dds_.MapMetaData_'
    data: types.sequence[types.uint8]


